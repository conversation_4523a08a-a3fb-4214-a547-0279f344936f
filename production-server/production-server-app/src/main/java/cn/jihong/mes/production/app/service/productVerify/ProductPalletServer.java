package cn.jihong.mes.production.app.service.productVerify;

import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.enums.OutboundVerifyEnum;
import cn.jihong.mes.production.api.model.enums.UnitEnum;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.service.IProductLastPalletService;
import cn.jihong.mes.production.api.service.IProductOutboundService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.api.service.IProductVerifyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
public class ProductPalletServer {

    @Resource
    private ProductVerifyFactory productVerifyFactory;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductLastPalletService productLastPalletService;



    public void verify(Long productTicketId,String planTicketNo, String machineName, BigDecimal producedQuantity) {
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        String unitCode = productOutboundService.getUnitCode(productTicketPO);
        // 当前工序的报工和出站的单位
        String unit = getUnitConversion(unitCode);

        IProductVerifyService productVerifyService =
                productVerifyFactory.getProductVerifyService(OutboundVerifyEnum.BASE_PAPER.getName());
        productVerifyService.verifyPallet(planTicketNo, machineName, unit,productTicketPO.getProcessCode(),producedQuantity);
    }


    private String getUnitConversion(String unitCode) {
        return UnitEnum.getUnitEnum(unitCode).getStandardCode().toUpperCase();
    }

    public boolean verifyPalletShortCode(String planTicketNo, String palletShortCode) {
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductPalletPO::getPlanTicketNo,planTicketNo)
                .eq(ProductPalletPO::getPalletShortCode,palletShortCode)
                .eq(ProductPalletPO::getCompanyCode, SecurityUtil.getCompanySite());
        List<ProductPalletPO> list = productLastPalletService.list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            return true;
        }
        return false;
    }
}
