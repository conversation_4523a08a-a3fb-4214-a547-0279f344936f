package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductLastPalletService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 上栈板信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productLastPallet")
@ShenyuSpringMvcClient(path = "/productLastPallet/**")
public class ProductLastPalletController {

    @Resource
    private IProductLastPalletService productLastPalletService;

    /**
     * 根据栈板码查询上栈板信息
     */
    @PostMapping("/getLastPalletByPalletCode")
    public StandardResult<GetLastPalletByPalletCodeOutVO> getLastPalletByPalletCode(@RequestBody @Valid GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productLastPalletService.getLastPalletByPalletCode(getLastPalletByPalletCodeInVO));
    }

    /**
     * 根据栈板短码查询栈板信息
     */
    @PostMapping("/getPalletByPalletShortCode")
    public StandardResult<GetLastPalletByPalletCodeOutVO> getPalletByPalletShortCode(@RequestBody @Valid GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getPalletByPalletShortCode(getLastPalletByPalletCodeInVO));
    }

    /**
     * 查询成品（报工）信息
     */
    @GetMapping("/getListByPalletSource")
    public StandardResult getListByPalletSource(@RequestBody GetListByPalletSourceInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getListByPalletSource(inVO));
    }

    /**
     * 保存上栈板信息
     */
    @PostMapping("/saveLastPalletInfo")
    public StandardResult saveLastPalletInfo(@RequestBody @Valid SaveLastPalletInfoInVO saveLastPalletInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.saveLastPalletInfo(saveLastPalletInfoInVO));
    }

    /**
     * 查询使用中的上栈板列表
     */
    @PostMapping("/getLastPalletList")
    public StandardResult<List<LastPalletOutVO>>
        getLastPalletList(@RequestBody @Valid GetByMachineNameInVO getByMachineNameInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productLastPalletService.getLastPalletListByMachineName(getByMachineNameInVO));
    }

    /**
     * 查询暂存中的上栈板列表
     */
    @PostMapping("/getStagingLastPalletList")
    public StandardResult<List<LastPalletOutVO>>
    getStagingLastPalletList(@RequestBody @Valid GetByMachineNameInVO getByMachineNameInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getStagingLastPalletListByMachineName(getByMachineNameInVO));
    }

    /**
     * 查询上栈板记录
     */
    @PostMapping("/getLastPalletRecords")
    public StandardResult<Pagination<LastPalletRecordsOutVO>> getLastPalletRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getLastPalletRecords(productTicketPageInVO));
    }

    /**
     * 查询下栈板记录
     */
    @PostMapping("/getDownPalletRecords")
    public StandardResult<Pagination<LastPalletRecordsOutVO>> getDownPalletRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getDownPalletRecords(productTicketPageInVO));
    }


    /**
     * 查询栈板使用列表
     */
    @PostMapping("/getPalletUseList")
    public StandardResult<Pagination<PalletUseOutVO>>
    getPalletUseList(@RequestBody @Valid GetPalletListPageInVO getPalletListPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getPalletUseList(getPalletListPageInVO));
    }

    /**
     * 查询栈板使用详情
     */
    @GetMapping("/getPalletUseDetial/{id}")
    public StandardResult<PalletUseOutVO>
    getPalletUseDetial(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getPalletUseDetial(id));
    }

    /**
     * 查询栈板使用详情列表
     */
    @PostMapping("/getPalletUseDetialList")
    public StandardResult<Pagination<PalletUseRecordOutVO>>
    getPalletUseDetialList(@RequestBody @Valid ProductInfoPageInVO productInfoPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getPalletUseDetialList(productInfoPageInVO));
    }

    /**
     * 下栈板
     */
    @PostMapping("/downPallet")
    public StandardResult downPallet(@RequestBody @Valid DownMaterialInVO downMaterialInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productLastPalletService.downPallet(downMaterialInVO));
    }



    /**
     * 暂存栈板
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/12/21 13:45
     */
    @PostMapping("/temporaryStoragePallet")
    public StandardResult temporaryStoragePallet(@RequestBody @Valid TemporaryStoragePalletInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productLastPalletService.temporaryStoragePallet(inVO));
    }

    /**
     * 修改栈板状态
     */
    @PostMapping("/updatePalletStatus")
    public StandardResult updatePalletStatus(@RequestBody @Valid UpdatePalletStatusInVO updatePalletStatusInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.updatePalletStatus(updatePalletStatusInVO));
    }

    /**
     * 获得栈板短码
     */
    @GetMapping("/getPalletShortCode/{id}")
    public StandardResult<String> getPalletShortCode(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productLastPalletService.getPalletShortCode(id));
    }


}

