package cn.jihong.mes.production.app.controller;

import cn.jihong.calibre.review.server.api.model.enums.InspectionPeopleJobEnum;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.enums.*;
import cn.jihong.mes.production.api.service.ICommonEnumService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController()
@RequestMapping("/common")
@ShenyuSpringMvcClient(path = "/common/**")
public class CommonEnumController {

    @Resource
    private ICommonEnumService iCommonEnumService;

    /**
     * 获得枚举
     * {@link FirstCheckStatusEnum}  首检结果
     * {@link MachineTaskTypeEnum}   机台任务状态
     * {@link MaterialEnum}  物料状态
     * {@link MaterialOperateTypeEnum}  物料操作类型
     * {@link MQBizCodeEnum}  MQ消息类型
     * {@link OutboundEnum}  出站状态
     * {@link PalletOperateStatusEnum}  栈板状态
     * {@link PalletOperateTypeEnum}  栈板操作类型
     * {@link ProductShitEnum}  班次
     * {@link ShiftNoEnum}  班次转换erp
     * {@link UnitEnum}   单位
     * {@link InspectionPeopleJobEnum}   首检角色
     * {@link CompanyBoxEnum}   客户箱码
     * {@link MachinePartsEnum}   机组 MachinePartsEnum
     * {@link StoreStatusEnum}   物料入库状态
     * {@link WorkShopEnum}   车间
     * {@link MeshTypeEnum}   网格类型
     *
     */
    @GetMapping("/enum/{enumType}")
    public StandardResult<List<EnumDTO>> getEnums(@PathVariable String enumType) {
        return StandardResult.resultCode
                (OperateCode.SUCCESS, iCommonEnumService.getEnumsByType(enumType));
    }


}
