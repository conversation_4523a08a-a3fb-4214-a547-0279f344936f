package cn.jihong.mes.production.app.service.logistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.common.convertor.PageConvertor;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.logistics.api.model.request.*;
import cn.jihong.logistics.api.model.response.*;
import cn.jihong.logistics.api.service.IBaseService;
import cn.jihong.logistics.api.service.ITileWireErpOrderService;
import cn.jihong.logistics.api.service.command.IJsqjHuShiOffsetPrintService;
import cn.jihong.logistics.api.service.command.IJsqjHuShiTileWireService;
import cn.jihong.logistics.api.service.command.IJsqjHuShiWatermarkService;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.constant.LogisticsConst;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.constant.SequenceConst;
import cn.jihong.mes.production.api.model.dto.LogisticsProductConfigDTO;
import cn.jihong.mes.production.api.model.dto.MachineLogisticsDTO;
import cn.jihong.mes.production.api.model.dto.MeshDTO;
import cn.jihong.mes.production.api.model.enums.MeshTypeEnum;
import cn.jihong.mes.production.api.model.enums.OutboundTypeEnum;
import cn.jihong.mes.production.api.model.enums.PalletOperateStatusEnum;
import cn.jihong.mes.production.api.model.enums.WorkShopEnum;
import cn.jihong.mes.production.api.model.po.*;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.in.logistics.*;
import cn.jihong.mes.production.api.model.vo.out.SysDictOutVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.GetDeviceOrderCarrayPauseStatusOutVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.ItemQueryByConditionOutVO;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.util.redis.RedisUtil;
import cn.jihong.message.api.service.IEnterpriseWeChatService;
import cn.jihong.oa.erp.api.model.po.PmaalTPO;
import cn.jihong.oa.erp.api.model.vo.*;
import cn.jihong.oa.erp.api.model.vo.out.EczaucTOutVO;
import cn.jihong.oa.erp.api.model.vo.portrait.CorporationGroupOutVO;
import cn.jihong.oa.erp.api.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-24 15:11
 */
@Slf4j
@DubboService
public class LogisticsImpl implements ILogisticsService {

    @DubboReference
    private ISfcbTService iSfcbTService;

    @DubboReference
    private ISfaaTService iSfaaTService;

    @DubboReference
    private IPmaalTService iPmaalTService;

    @DubboReference
    private IProductionMachineService iProductionMachineService;

    @DubboReference
    private IJsqjHuShiWatermarkService iJsqjHuShiWatermarkService;

    @DubboReference
    private IJsqjHuShiOffsetPrintService iJsqjHuShiOffsetPrintService;

    @DubboReference
    private IJsqjHuShiTileWireService iJsqjHuShiTileWireService;


    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IPmaalTService pmaalTService;
    @DubboReference
    private IImaalTService imaalTService;

    @Resource
    private RedisTemplate redisTemplate;


    @Resource
    private IProductLastPalletService productLastPalletService;
    @DubboReference
    private IProductionMachineService productionMachineService;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductMachineTaskService productMachineTaskService;
    @Resource
    private IMeshService meshService;

    @DubboReference
    private IProductionMachineLogisticsConfigService productionMachineLogisticsConfigService;
    @DubboReference
    private IEnterpriseWeChatService enterpriseWeChatService;

    @DubboReference
    private ITileWireErpOrderService iTileWireErpOrderService;

    @Resource
    private IProductTicketService iProductTicketService;

    private Map<String,IBaseService> logisticsContext = new HashMap<>();

    @DubboReference
    private IOocqlTService iOocqlTService;

    @DubboReference
    private IEczaucTService iEczaucTService;
    @Resource
    private ILogisticsProductConfigService logisticsProductConfigService;
    @Resource
    private ISysDictService sysDictService;
    @Resource
    private IProductTicketService productTicketService;


    @PostConstruct
    private void init(){
        logisticsContext.put(WorkShopEnum.HUSHI_WATERMARK.getCode(),iJsqjHuShiWatermarkService);
        logisticsContext.put(WorkShopEnum.HUSHI_OFFSET_PRINT.getCode(),iJsqjHuShiOffsetPrintService);
    }

    @Override
    public String buildErpOrderId(String planTickNo, String processCode) {
//        List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(planTickNo);
        List<CorporationGroupOutVO> corporationGroupOutVOS = iOocqlTService.getOocqlTByType("221", processCode);
        if (CollectionUtil.isEmpty(corporationGroupOutVOS)) {
            throw new CommonException(String.format("未找到%s工序", processCode));
        }
        CorporationGroupOutVO corporationGroupOutVO = corporationGroupOutVOS.get(0);
        return planTickNo + "@" + corporationGroupOutVO.getOocql004();
    }

    @Override
    public List<MeshDeviceResponse> meshDeviceDetails() {
        return iJsqjHuShiWatermarkService.meshDeviceDetails().getData();
    }

    @Override
    public Boolean syncProductionPlanning(String workshopCode,SyncProductionPlanningInVO inVO) {

//        JingShanLightBaseRequest<List<ApiCreateProducePlansItemRequest>> request = new JingShanLightBaseRequest<>();
//        List<ApiCreateProducePlansItemRequest> list = new ArrayList<>();
//        ApiCreateProducePlansItemRequest data1 = new ApiCreateProducePlansItemRequest();
//        data1.setMeshId(2001);
//        data1.setProduceCount(10000);
//        data1.setErpOrderId("125-S299-24060000059*097*WB001*1");
//        data1.setSequenceNo(1);
//        list.add(data1);
//        request.setData(list);
//        StandardResult<Boolean> standardResult = logisticsContext.get(workshopCode).syncProductionPlanning(request);
//        return standardResult.getData();
        return true;
    }

    @Override
    public String callForMaterial(Long productTicketId,String erpOrderId) {
        // 匹配erpOrderId 在物流配置中对应的供料网带口
        List<String> preProcessCodeList = new ArrayList<>();
        if(erpOrderId.contains("@")) {
            String preProcessName = erpOrderId.split("@")[1];
            List<String> preProcessNameList = new ArrayList<>();
            preProcessNameList.add(preProcessName);
            List<GetOocqlTByProcessNameOutVO> oocqlTByProcessNameList = iOocqlTService.getOocqlTByProcessName(SecurityUtil.getCompanySite(), preProcessNameList);
            preProcessCodeList = oocqlTByProcessNameList.stream().map(GetOocqlTByProcessNameOutVO::getOocq002).collect(Collectors.toList());
        }else {
            ProductTicketPO productTicketPO = iProductTicketService.getById(productTicketId);
            SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(),productTicketPO.getProcessCode());
            if("MULT".equals(sfcbTVO.getSfcb007())){
                List<SfcbTVO> processList = iSfcbTService.getByTickNo(productTicketPO.getPlanTicketNo());
                List<SfcbTVO> previousProcessList = processList.stream().filter(t -> t.getSfcb009().equals(productTicketPO.getProcessCode())).collect(Collectors.toList());
                preProcessCodeList.addAll(previousProcessList.stream().map(SfcbTVO::getSfcb003).filter(LogisticsConst.TILE_WIRE_PROCESS::contains).collect(Collectors.toList()));
            }else {
                preProcessCodeList.add(sfcbTVO.getSfcb007());
            }
        }
        log.info("preProcessCodeList:{}", JSON.toJSONString(preProcessCodeList));
        StringBuilder errorSb = new StringBuilder();
        MachineLogisticsDTO logisticsDTO = getMachineLogistics(productTicketId);
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList = logisticsDTO.getMeshInfoList().stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
        boolean accord = false;
        for(MachineLogisticsDTO.MeshInfo supplyMesh : supplyMeshList) {
            if(preProcessCodeList.contains(supplyMesh.getProcessCode())) {
                accord = true;
                JingShanLightBaseRequest<RequestCarryInstructionRequest> request = new JingShanLightBaseRequest<>();
                RequestCarryInstructionRequest data = new RequestCarryInstructionRequest();
                data.setErpOrderId(erpOrderId);
                data.setOrderId("");
                data.setCount(0);
                data.setRequestType(1);
                data.setTargetAddress(Integer.parseInt(supplyMesh.getMeshCode()));
                request.setData(data);
                StandardResult<CallForMaterialResponse> standardResult = logisticsContext.get(logisticsDTO.getWorkshopCode()).callForMaterial(request);
                CallForMaterialResponse callForMaterialResponse = standardResult.getData();
                if (!callForMaterialResponse.getStatus()) {
                    errorSb.append(erpOrderId + ":叫料失败;");
                }
            }
        }
        if(!accord){
            errorSb.append("erp与当前物流的上一道工序不一致");
        }
        return errorSb.toString();
    }

    /**
     *  获取物流平台相关信息
     * @param productTicketId
     * @return: cn.jihong.mes.production.api.model.dto.MachineLogisticsDTO
     * <AUTHOR>
     * @date: 2025-03-12 13:53
     */
    private MachineLogisticsDTO getMachineLogistics(Long productTicketId) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(productTicketId);
        ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(productTicketPO.getMachineName(), productTicketPO.getProcessCode());
        AssertUtil.isNotNull(machineConfig,"尚未配置该机台物流网带");
        MachineLogisticsDTO dto = new MachineLogisticsDTO();
        dto.setWorkshopCode(machineConfig.getWorkshopCode());
        dto.setMachineName(productTicketPO.getMachineName());
        AssertUtil.isNotBlank(machineConfig.getSupplyMeshCode(),"尚未配置该机台物流供料网带");
        List<MachineLogisticsDTO.MeshInfo> meshInfoList = new ArrayList<>();
        String[] supplyMeshSplit = machineConfig.getSupplyMeshCode().split(",");
        for(String supplyMesh : supplyMeshSplit) {
            MachineLogisticsDTO.MeshInfo meshInfo = new MachineLogisticsDTO.MeshInfo();
            meshInfo.setType(1);
            if(supplyMesh.contains(":")) {
                String[] split = supplyMesh.split(":");
                meshInfo.setMeshCode(split[0]);
                if(split.length == 2) {
                    meshInfo.setProcessCode(split[1]);
                }else {
                    List<SfcbTVO> processList = iSfcbTService.getByTickNo(productTicketPO.getPlanTicketNo());
                    List<SfcbTVO> previousProcessList = processList.stream().filter(t -> t.getSfcb009().equals(productTicketPO.getProcessCode())).collect(Collectors.toList());
                    AssertUtil.isNotEmpty(previousProcessList,"ERP中尚未配置该工序的上一道工序");
                    meshInfo.setProcessCode(previousProcessList.get(0).getSfcb003());
                }
            }else {
                meshInfo.setMeshCode(supplyMesh);
            }
            meshInfoList.add(meshInfo);
        }
        if (StringUtils.isNotBlank(machineConfig.getExitMeshCode())) {
            String[] exitMeshSplit = machineConfig.getExitMeshCode().split(",");
            for(String exitMesh : exitMeshSplit) {
                MachineLogisticsDTO.MeshInfo meshInfo = new MachineLogisticsDTO.MeshInfo();
                meshInfo.setType(2);
                if(exitMesh.contains(":")) {
                    String[] split = exitMesh.split(":");
                    meshInfo.setMeshCode(split[0]);
                    if(split.length == 2) {
                        meshInfo.setProcessCode(split[1]);
                    }else {
                        meshInfo.setProcessCode(productTicketPO.getProcessCode());
                    }
                }else {
                    meshInfo.setMeshCode(exitMesh);
                }
                meshInfoList.add(meshInfo);
            }
        }
        dto.setMeshInfoList(meshInfoList);
        return dto;
    }

    @Override
    public List<ApiMeshDeviceScadaltemResponse> returnOfMaterialMeshList(ReturnOfMaterialMeshListInVO inVO) {
        String workshopCode;
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList;
        if (Objects.nonNull(inVO.getProductTicketId())) {
            MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
            supplyMeshList = logisticsDTO.getMeshInfoList().stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
            workshopCode = logisticsDTO.getWorkshopCode();
        }else {
             AssertUtil.isNotBlank(inVO.getMachineName(),"机台名称不能为空");
             ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(inVO.getMachineName());
             supplyMeshList = getSupplyMeshInfoList(machineConfig);
             workshopCode = machineConfig.getWorkshopCode();
        }
        List<ApiMeshDeviceScadaltemResponse> list = new ArrayList<>();
        for(MachineLogisticsDTO.MeshInfo supplyMesh : supplyMeshList) {
            JingShanLightBaseRequest<SearchBufferBySameTrackRequest> request = new JingShanLightBaseRequest<>();
            SearchBufferBySameTrackRequest data = new SearchBufferBySameTrackRequest();
            data.setMeshId(supplyMesh.getMeshCode());
            request.setData(data);
            StandardResult<List<ApiMeshDeviceScadaltemResponse>> standardResult = logisticsContext.get(workshopCode).returnOfMaterialMeshList(request);
            list.addAll(standardResult.getData());
        }
        return list;
    }

    private static List<MachineLogisticsDTO.MeshInfo> getSupplyMeshInfoList(ProductionMachineLogisticsConfigPO machineConfig) {
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList = new ArrayList<>();
        String[] supplyMeshSplit = machineConfig.getSupplyMeshCode().split(",");
        for(String supplyMesh : supplyMeshSplit) {
            MachineLogisticsDTO.MeshInfo meshInfo = new MachineLogisticsDTO.MeshInfo();
            meshInfo.setType(1);
            if(supplyMesh.contains(":")) {
                String[] split = supplyMesh.split(":");
                meshInfo.setMeshCode(split[0]);
            }else {
                meshInfo.setMeshCode(supplyMesh);
            }
            supplyMeshList.add(meshInfo);
        }
        return supplyMeshList;
    }

    @Override
    public Boolean returnOfMaterial(ReturnOfMaterialInVO inVO) {
        String workshopCode;
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList;
        if (Objects.nonNull(inVO.getProductTicketId())) {
            MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
            supplyMeshList = logisticsDTO.getMeshInfoList().stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
            workshopCode = logisticsDTO.getWorkshopCode();
        }else {
            AssertUtil.isNotBlank(inVO.getMachineName(),"机台名称不能为空");
            ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(inVO.getMachineName());
            supplyMeshList = getSupplyMeshInfoList(machineConfig);
            workshopCode = machineConfig.getWorkshopCode();
        }
        for(MachineLogisticsDTO.MeshInfo supplyMesh : supplyMeshList) {
            JingShanLightBaseRequest<ManualBackToBufferRequest> request = new JingShanLightBaseRequest<>();
            ManualBackToBufferRequest data = getManualBackToBufferRequest(inVO);
            data.setSourceMeshId(Integer.parseInt(supplyMesh.getMeshCode()));
            request.setData(data);
            StandardResult<Boolean> standardResult = logisticsContext.get(workshopCode).returnOfMaterial(request);
        }
        return true;
    }

    private ManualBackToBufferRequest getManualBackToBufferRequest(ReturnOfMaterialInVO inVO) {
        ProductTicketPO productTicketPO = iProductTicketService.getById(inVO.getProductTicketId());
        GetErpOrderIdListInVO getErpOrderIdListInVO = new GetErpOrderIdListInVO();
        getErpOrderIdListInVO.setMachineName(productTicketPO.getMachineName());
        getErpOrderIdListInVO.setProcess(productTicketPO.getProcess());
        getErpOrderIdListInVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        List<String> erpOrderIdList = getErpOrderIdList(getErpOrderIdListInVO);

        ProductPalletPO productPalletPO = productLastPalletService.getByPalletCode(inVO.getPalletId());
        ManualBackToBufferRequest data = new ManualBackToBufferRequest();
        data.setPalletId(productPalletPO.getIdsPalletId());
        data.setTargetMeshId(inVO.getTargetMeshId());
        data.setErpOrderId(CollectionUtil.isNotEmpty(erpOrderIdList)?erpOrderIdList.get(0):"");
        data.setStatusTag(inVO.getStatusTag());
        data.setSourceMeshId(inVO.getSourceMeshId());
        data.setTrackIndex(inVO.getTrackIndex());
        data.setPalletColumn(inVO.getPalletColumn());
        data.setPalletLength(inVO.getPalletLength());
        data.setPalletWidth(inVO.getPalletWidth());
        data.setCreatedBy(inVO.getCreatedBy());
        data.setPaperStackCount(inVO.getPaperStackCount());
        return data;
    }

    @Override
    public Boolean startCallMaterial(StartCallMaterialInVO inVO) {
        MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList = logisticsDTO.getMeshInfoList().stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
        for(MachineLogisticsDTO.MeshInfo supplyMesh : supplyMeshList) {
            JingShanLightBaseRequest<StartDeviceOrderCarrayExecuteRequest> request = new JingShanLightBaseRequest<>();
            StartDeviceOrderCarrayExecuteRequest data = new StartDeviceOrderCarrayExecuteRequest();
            data.setMeshId(supplyMesh.getMeshCode());
            request.setData(data);
            StandardResult<Boolean> standardResult = logisticsContext.get(logisticsDTO.getWorkshopCode()).startCallMaterialByMeshId(request);
        }
        return true;
    }

    @Override
    public Boolean pauseCallMaterial(PauseCallMaterialInVO inVO) {
        MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList = logisticsDTO.getMeshInfoList().stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
        for(MachineLogisticsDTO.MeshInfo supplyMesh : supplyMeshList) {
            JingShanLightBaseRequest<PauseDeviceOrderCarrayExecuteRequest> request = new JingShanLightBaseRequest<>();
            PauseDeviceOrderCarrayExecuteRequest data = new PauseDeviceOrderCarrayExecuteRequest();
            data.setMeshId(supplyMesh.getMeshCode());
            request.setData(data);
            StandardResult<Boolean> standardResult = logisticsContext.get(logisticsDTO.getWorkshopCode()).pauseCallMaterialByMeshId(request);
        }
        return true;
    }

    @Override
    public List<ApiOrderCarryInstructionResponse> getInstructionByTargetDeviceId(GetInstructionByTargetDeviceIdInVO inVO) {
        MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
        JingShanLightBaseRequest<GetInstructionByTargetDeviceIdRequest> request = new JingShanLightBaseRequest<>();
        GetInstructionByTargetDeviceIdRequest data = new GetInstructionByTargetDeviceIdRequest();
        data.setTargetAddress(inVO.getTargetMeshId());
        request.setData(data);
        StandardResult<List<ApiOrderCarryInstructionResponse>> standardResult = logisticsContext.get(logisticsDTO.getWorkshopCode()).getInstructionByTargetDeviceId(request);
        return standardResult.getData();
    }

    @Override
    public Boolean cancelOrderCarry(CancelOrderCarryInVO inVO) {
        MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
        JingShanLightBaseRequest<CancelOrderCarryRequest> request = new JingShanLightBaseRequest<>();
        CancelOrderCarryRequest data = new CancelOrderCarryRequest();
        data.setOrderCarryInstructionId(inVO.getOrderCarryInstructionId());
        request.setData(data);
        StandardResult<Boolean> standardResult = logisticsContext.get(logisticsDTO.getWorkshopCode()).cancelOrderCarry(request);
        return standardResult.getData();
    }

    @Override
    public List<GetDeviceOrderCarrayPauseStatusOutVO> getDeviceOrderCarrayPauseStatus(GetDeviceOrderCarrayPauseStatusInVO inVO) {
        MachineLogisticsDTO logisticsDTO = getMachineLogistics(inVO.getProductTicketId());
        List<MachineLogisticsDTO.MeshInfo> supplyMeshList = logisticsDTO.getMeshInfoList().stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
        List<GetDeviceOrderCarrayPauseStatusOutVO> list = new ArrayList<>();
        for(MachineLogisticsDTO.MeshInfo supplyMesh : supplyMeshList) {
            JingShanLightBaseRequest<GetDeviceOrderCarrayPauseStatusRequest> request = new JingShanLightBaseRequest<>();
            GetDeviceOrderCarrayPauseStatusRequest data = new GetDeviceOrderCarrayPauseStatusRequest();
            data.setMeshId(supplyMesh.getMeshCode());
            request.setData(data);
            StandardResult<Boolean> standardResult = logisticsContext.get(logisticsDTO.getWorkshopCode()).getDeviceOrderCarrayPauseStatus(request);
            GetDeviceOrderCarrayPauseStatusOutVO outVO = new GetDeviceOrderCarrayPauseStatusOutVO();
            outVO.setMeshId(supplyMesh.getMeshCode());
            // 0：开启 1:暂停 2：无供料计划
            if (standardResult.getData() != null) {
                outVO.setStatus(standardResult.getData() ? 1 : 0);
            }else {
                outVO.setStatus(2);
            }
            list.add(outVO);
        }
        return list;
    }


    @Override
    public Pagination<ItemQueryByConditionOutVO> itemQueryByCondition(ItemQueryByConditionInVO inVO) {
        JingShanLightBaseRequest<SearchPalletsByConditionRequest> request = new JingShanLightBaseRequest<>();
        SearchPalletsByConditionRequest data = new SearchPalletsByConditionRequest();
        data.setStart(inVO.getPageNum());
        data.setCount(inVO.getPageSize());

        SearchPalletsByConditionRequest.PalletSearcherCondition searcherCondition = new SearchPalletsByConditionRequest.PalletSearcherCondition();
        searcherCondition.setStartAt(inVO.getStartAt());
        searcherCondition.setEndAt(inVO.getEndAt());
        data.setSearcherCondition(searcherCondition);
        request.setData(data);

        StandardResult<JingShanLightBaseResponse<JSONObject>> response = logisticsContext.get(inVO.getWorkshopCode()).itemQueryByCondition(request);
        JSONObject resObj = response.getData().getData();
        if(resObj!=null) {
            List<ApiPalletResponse> list = resObj.getJSONArray("SeracherData").toJavaList(ApiPalletResponse.class);
            IPage ipage = PageConvertor.toPage(inVO.getPageNum().longValue(), inVO.getPageSize().longValue());
            ipage.setRecords(BeanUtil.copyToList(list, ItemQueryByConditionOutVO.class));
            return Pagination.newInstance(ipage.getRecords(), resObj.getLong("TotalRecord"),(resObj.getLong("TotalRecord") + inVO.getPageSize() -1 ) /inVO.getPageSize());
        }else {
            return Pagination.newInstance(null);
        }
    }

    @Override
    public GetBoardInfoJstResponse getBoardInfoJst() {
        StandardResult<GetBoardInfoJstResponse> response = iJsqjHuShiTileWireService.getBoardInfoJst();
        return response.getData();
    }

    @Override
    public List<GetOrdersDataResponse> getOrdersData() {
        StandardResult<List<GetOrdersDataResponse>> response = iJsqjHuShiTileWireService.getOrdersData();
        return response.getData();
    }

    @Override
    public List<GetRealtimeDataResponse> getRealtimeData() {
        StandardResult<List<GetRealtimeDataResponse>> response = iJsqjHuShiTileWireService.getRealtimeData();
        return response.getData();
    }

    @Override
    public List<GetStackeredResponse> getStackered() {
        StandardResult<List<GetStackeredResponse>> response = iJsqjHuShiTileWireService.getStackered();
        return response.getData();
    }

    @Override
    public GetSearchStackeredResponse getSearchStackered(String searchId) {
        StandardResult<GetSearchStackeredResponse> response = iJsqjHuShiTileWireService.getSearchStackered(searchId);
        return response.getData();
    }

    @Override
    public List<PostOrderResponse> postOrder(List<ProductionPlanPO> productionPlanPOS) {
        String processName = productionPlanPOS.get(0).getProductionProcess();
        List<String> processNameList = new ArrayList<>();
        processNameList.add(processName);
        Map<String, SfaaTVO> mapInfoByTicket = iSfaaTService.getMapInfoByTicket(productionPlanPOS.stream().map(ProductionPlanPO::getWorkerOrderNo).collect(Collectors.toList()));
//        List<String> customerList = mapInfoByTicket.values().stream().map(SfaaTVO::getSfaa009).filter(Objects::nonNull).collect(Collectors.toList());
//        Map<String, PmaalTPO> customerInfoMap = iPmaalTService.getCustomerInfo(customerList);
        JingShanLightBaseRequest<PostOrderRequest> request = new JingShanLightBaseRequest<>();
        PostOrderRequest postOrderRequest = new PostOrderRequest();

        List<PostOrderRequest.PostOrderData> list = new ArrayList<>();
        for(ProductionPlanPO productionPlanPO : productionPlanPOS) {
            PostOrderRequest.PostOrderData data = new PostOrderRequest.PostOrderData();
            data.setCmdtyp("A");
            data.setSerial(productionPlanPO.getWorkerOrderNo());

            SfaaTVO sfaaTVO = mapInfoByTicket.get(productionPlanPO.getWorkerOrderNo());
            AssertUtil.isNotNull(sfaaTVO,"ERP查询不到工单的单据信息");

            EczaucTOutVO eczaucTOutVO = iEczaucTService.selectOneByMaterialCode(sfaaTVO.getSfaa010(), sfaaTVO.getSfaa016());
            if(eczaucTOutVO == null){
                eczaucTOutVO = iEczaucTService.selectOneByMaterialCode("202020022218", "01");
            }
            AssertUtil.isNotNull(eczaucTOutVO,"ERP中没维护" + processName + "给瓦线物流");

            String customerNo = sfaaTVO.getSfaa009();
            if(StringUtils.isNotBlank(customerNo)) {
                data.setClntcde(customerNo);
                data.setClntnme(productionPlanPO.getProductionName());
           //     PmaalTPO pmaalTPO = customerInfoMap.get(customerNo);
//                AssertUtil.isNotNull(pmaalTPO, "ERP查询不到工单的客户名称");
//                data.setClntnme(pmaalTPO.getPmaal004());
            }else{
                data.setClntcde("0");
                data.setClntnme(productionPlanPO.getProductionName());
            }
            data.setMatcde(eczaucTOutVO.getEczauc003());
            data.setCrrcde(eczaucTOutVO.getEczauc004());
            data.setBwidth(eczaucTOutVO.getEczauc005());
            data.setSplitw(eczaucTOutVO.getEczauc006());
            data.setPrstyp(eczaucTOutVO.getEczauc007());
            data.setLine01(eczaucTOutVO.getEczauc008());
            data.setLine02(eczaucTOutVO.getEczauc009());
            data.setLine03(eczaucTOutVO.getEczauc010());
            data.setLine04(eczaucTOutVO.getEczauc011());
            data.setLine05(eczaucTOutVO.getEczauc012());
            data.setLine06(eczaucTOutVO.getEczauc013());
            data.setLine07(eczaucTOutVO.getEczauc014());
            data.setSplitw2(0);
            data.setLine08(0);
            data.setLine09(0);
            data.setLine10(0);
            data.setLine11(0);
            data.setLine12(0);
            data.setFringl(eczaucTOutVO.getEczauc015());
            data.setFringw(eczaucTOutVO.getEczauc016());
            data.setAccnum(eczaucTOutVO.getEczauc017());
            data.setTrynum(eczaucTOutVO.getEczauc018());
            data.setAddress("0");
            data.setStkmod(0);
            data.setPacknum(0);
            data.setRemark("0");
            data.setNxtprc("0");
            data.setObjtyp("0");
            data.setCrtnme("0");
            data.setPrdnme("0");
            data.setSpecs("0");
            data.setSchno(productionPlanPO.getId().toString());
            data.setQuence(0);
            data.setPaper1("0");
            data.setPaper2("0");
            data.setPaper3("0");
            data.setPaper4("0");
            data.setPaper5("0");
            data.setPaper6("0");
            data.setPaper7("0");
//        data.setSt(DateUtil.format(LocalDateTime.now().minusDays(1), DatePattern.NORM_DATE_PATTERN));
//        data.setEt(DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATE_PATTERN));
            list.add(data);
        }
        postOrderRequest.setData(list);
        request.setData(postOrderRequest);
        StandardResult<List<PostOrderResponse>> standardResult = iJsqjHuShiTileWireService.postOrder(request);
        return standardResult.getData();
    }

    @Override
    public List<GetFOrderToErpResponse> getFOrderToErp(GetFOrderToErpRequest request) {
        JingShanLightBaseRequest<GetFOrderToErpRequest> baseRequest = new JingShanLightBaseRequest<>();
        baseRequest.setData(request);
        StandardResult<List<GetFOrderToErpResponse>> standardResult = iJsqjHuShiTileWireService.getFOrderToErp(baseRequest);
        return standardResult.getData();
    }

    @Override
    public List<String> getErpOrderIdList(GetErpOrderIdListInVO inVO) {
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(inVO.getMachineName(), inVO.getProcess());
        if (productionMachineOutVO == null) {
            throw new CommonException("查无此设备，请在机台管理中维护，机台名称：" + inVO.getMachineName() + "工序：" + inVO.getProcess());
        }
        List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(inVO.getPlanTicketNo());
        GetProcessSeqByTickNoOutVO outVO = processSeqList.stream().filter(t -> t.getSfcb003().equals(productionMachineOutVO.getProcessCode()))
                .findFirst().orElseThrow(() -> new CommonException("ERP查询不到该工序"));
        if(LogisticsConst.TILE_WIRE_PROCESS.contains(outVO.getSfcb003())){
            // 当前工序为抄瓦
            return null;
        }
        if(LogisticsConst.SLITTER_PROCESS.contains(outVO.getSfcb003())){
            // 当前工序为分切
            return null;
        }

        if(LogisticsConst.TILE_WIRE_PROCESS.contains(outVO.getSfcb007())){
           // 呼市  上个工序为抄瓦工序 则从物流平台获取 erpOrderId
//            ProductionMachineLogisticsConfigPO logisticsConfigPO = productionMachineLogisticsConfigService.getByMachineId(productionMachineOutVO.getId());
//            AssertUtil.isNotNull(logisticsConfigPO,"该机台尚未配置物流机台网带");
//            List<String> list = iTileWireErpOrderService.queryErpOrderIdList(inVO.getPlanTicketNo(), logisticsConfigPO.getSupplyMeshCode());
//            AssertUtil.isNotEmpty(list,"查询不到机台网带中对应瓦线该工单的出站信息");
//            return list;
           return List.of(inVO.getPlanTicketNo());

        }
        if("MULT".equals(outVO.getSfcb007())){
            return getPrevioutErpOrderIdList(inVO.getPlanTicketNo(), outVO.getSfcb003());
        }
        List<CorporationGroupOutVO> oocqlTByType = iOocqlTService.getOocqlTByType("221", outVO.getSfcb007());
        return List.of(inVO.getPlanTicketNo() + "@" + oocqlTByType.get(0).getOocql004());
    }

    /**
     * 获取当前工序 上一个工序出站的erpOrderId列表
     * @param planTicketNo
     * @param processCode
     * @return: java.util.List<java.lang.String>
     * <AUTHOR>
     * @date: 2025-03-14 10:22
     */
    @Override
    public List<String> getPrevioutErpOrderIdList(String planTicketNo, String processCode) {
        List<SfcbTVO> processList = iSfcbTService.getByTickNo(planTicketNo);
        List<SfcbTVO> previousProcessList = processList.stream().filter(t -> t.getSfcb009().equals(processCode)).collect(Collectors.toList());
        List<String> erpOrderIdList = new ArrayList<>();
        previousProcessList.forEach(t->{
            List<CorporationGroupOutVO> oocqlTByType = iOocqlTService.getOocqlTByType("221", t.getSfcb003());
            if(LogisticsConst.TILE_WIRE_PROCESS.contains(oocqlTByType.get(0).getOocql002())) {
                erpOrderIdList.add(planTicketNo);
            }else {
                erpOrderIdList.add(planTicketNo + "@" + oocqlTByType.get(0).getOocql004());
            }
        });
        return erpOrderIdList;
    }

    // ----------------------------------

    @Override
    public String generatePalletId(String planTickNo, String processCode) {
        String erpOrder = buildErpOrderId(planTickNo, processCode);
        // 使用Redis加锁
        boolean lockAcquired = acquireLock(SequenceConst.CREATE_PALLET_LOCK_KEY + ":" + erpOrder);
        if (!lockAcquired) {
            throw new CommonException("系统繁忙，请稍后再试");
        }

        try {
            // 使用 Redis 生成流水号
            String key = SequenceConst.CREATE_PALLET_ORDER_KEY + ":" + erpOrder;
            Long serialNumber = redisTemplate.opsForValue().increment(key);
            // 当第一个，则去数据库查询一下是否已经存在了
            if (serialNumber == 1) {
                erpOrder = palletCodeExist(erpOrder, serialNumber);
                log.info("最终的流水号：" + serialNumber);
                redisTemplate.opsForValue().setIfAbsent(key, serialNumber, 30, TimeUnit.DAYS);
                return erpOrder;
            }
            // 返回流水号
            return erpOrder + "@" +  String.format("%04d", serialNumber);
        } finally {
            // 释放锁
            releaseLock(SequenceConst.CREATE_PALLET_LOCK_KEY + ":" + erpOrder);
        }
    }




    private String palletCodeExist(String erpOrder, Long serialNumber) {
        ProductPalletPO productPalletPO = productLastPalletService.getByPalletCode(erpOrder + String.format("%04d", serialNumber));
        // 如果存在，则直接+500
        if (productPalletPO != null) {
            log.info("流水号：" + serialNumber + "已存在，+500");
            serialNumber += 500;
            palletCodeExist(erpOrder,serialNumber);
        }
        return erpOrder + "@" +  String.format("%04d", serialNumber);
    }

//    @Override
//    public StandardResult internetTape(String planTickNo, String processCode,
//                                               String machineName, BigDecimal palletQuquantity,
//                                               String palletId,BigDecimal plannedProduct
//    ) {
//        ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(machineName, processCode);
//        String erpOrderId = palletId.substring(0, palletId.lastIndexOf("@"));
//
//
//        LogisticsProductConfigDTO logisticsProductConfigDTO = new LogisticsProductConfigDTO();
//        logisticsProductConfigDTO.setPlanTicketNo(planTickNo);
//        logisticsProductConfigDTO.setMachineName(machineName);
//        logisticsProductConfigDTO.setProcessCode(processCode);
//
//        LogisticsProductConfigDTO logisticsProductConfig = logisticsProductConfigService.getLogisticsProductConfig(logisticsProductConfigDTO);
//        JingShanLightBaseRequest<ApiCreatedPalletItemRequest> request = new JingShanLightBaseRequest<ApiCreatedPalletItemRequest>();
//        ApiCreatedPalletItemRequest data = new ApiCreatedPalletItemRequest();
//
//
//        data.setErpPalletId(palletId);
//        data.setCategoryId(logisticsProductConfig.getMaterialCategory());
//        // 托 盘 状 态 ： GOOD=0 良 品 ， BAD=1 废 品 ，
//        //RESTACKING=2 待整理，ABNORMAL=3 不正常
//        data.setStatusTag(0);
////        data.setMaterialType(0);
////        data.setMergerMode(0);
////        data.setCurrentSpan(0);
//        data.setPlaceIndex(0); // 默认0
//        data.setPalletLength(logisticsProductConfig.getPalletLength());
//        data.setPalletWidth(logisticsProductConfig.getPalletWidth());
//        data.setPalletColumn(BigDecimal.ONE);
//        String deviceId = null;
//        if (StringUtils.isNotBlank(machineConfig.getExitMeshCode())) {
//            String[] split = machineConfig.getExitMeshCode().split(",");
//            deviceId = split[0];
//            if (deviceId.contains(":")) {
//                deviceId = deviceId.split(":")[0];
//            }
//        }
//        data.setEntranceDeviceId(deviceId); // 去机台管理中查询，第一个网待ID
//        data.setCreateBy("MES");
////        data.setCreateDetailInformation("");
//
//        List<ApiCreatedPalletItemRequest.ApiCreatedPaperStackItem> apiCreatedPaperStackItems = Lists.newArrayList();
//        ApiCreatedPalletItemRequest.ApiCreatedPaperStackItem apiCreatedPaperStackItem = new ApiCreatedPalletItemRequest.ApiCreatedPaperStackItem();
//        apiCreatedPaperStackItem.setErpOrderId(erpOrderId);
//        apiCreatedPaperStackItem.setEntranceDeviceId(deviceId); // 第一个网待ID
////        apiCreatedPaperStackItem.setPaperStackCodeBar("");
////        apiCreatedPaperStackItem.setScheduleId("");
////        apiCreatedPaperStackItem.setPlaceIndex(0);
//        apiCreatedPaperStackItem.setCount(palletQuquantity);
//        apiCreatedPaperStackItem.setStackLength(logisticsProductConfig.getStackLength());
//        apiCreatedPaperStackItem.setStackWidth(logisticsProductConfig.getStackWidth());
//        apiCreatedPaperStackItem.setStackColumn(logisticsProductConfig.getPalletColumns());
//        apiCreatedPaperStackItem.setOrderItem(new ApiCreatedPalletItemRequest.ApiCreatedOrderItem());
//        apiCreatedPaperStackItem.getOrderItem().setErpOrderId(erpOrderId);
//        apiCreatedPaperStackItem.getOrderItem().setErpWorkshopNo(planTickNo);
////        apiCreatedPaperStackItem.getOrderItem().setPreviewTargetAddress(0);
////        apiCreatedPaperStackItem.getOrderItem().setPreviewAreaId("");
////        apiCreatedPaperStackItem.getOrderItem().setForceTargetAddress(0);
////        apiCreatedPaperStackItem.getOrderItem().setForceAreaId("");
//
//        // 查询订单信息
//        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTickNo);
//        if (sfaaTVO == null) {
//            throw new CommonException("工程单不存在");
//        }
//        // 获得客户集团信息
//        PmaalTPO pmaalTPO = pmaalTService.getCustomerInfo(sfaaTVO.getSfaa009());
//        ImaalTVO imaalTVO = imaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
//        if (imaalTVO != null) {
//            apiCreatedPaperStackItem.getOrderItem().setProdDesc(imaalTVO.getImaal003());
//            apiCreatedPaperStackItem.getOrderItem().setProdDescShort(logisticsProductConfig.getProductShortName());
//        }
//        apiCreatedPaperStackItem.getOrderItem().setRemarks("");
//        apiCreatedPaperStackItem.getOrderItem().setCustID(sfaaTVO.getSfaa009());
//        if (pmaalTPO != null) {
//            apiCreatedPaperStackItem.getOrderItem().setCustName(pmaalTPO.getPmaal003());
//            apiCreatedPaperStackItem.getOrderItem().setCustNameShort(logisticsProductConfig.getCustomerShortName());
//        }
//        apiCreatedPaperStackItem.getOrderItem().setBoardQuality("");
//        apiCreatedPaperStackItem.getOrderItem().setSheetLength(logisticsProductConfig.getStackLength());
//        apiCreatedPaperStackItem.getOrderItem().setSheetWidth(logisticsProductConfig.getStackWidth());
//        String pppp = String.valueOf(plannedProduct);
//        pppp = pppp.substring(0, pppp.indexOf("."));
//        apiCreatedPaperStackItem.getOrderItem().setPlanCount(BigDecimal.valueOf(Long.parseLong(pppp)));
//        apiCreatedPaperStackItem.getOrderItem().setCategoryId(logisticsProductConfig.getMaterialCategory());
////        apiCreatedPaperStackItem.getOrderItem().setFlute("");
//
//        apiCreatedPaperStackItems.add(apiCreatedPaperStackItem);
//        data.setPaperStacks(apiCreatedPaperStackItems);
//
//        request.setData(data);
//        StandardResult<PalletResponse> palletResponseStandardResult =
//                logisticsContext.get(machineConfig.getWorkshopCode()).internetTape(request);
//        return StandardResult.resultCode(OperateCode.SUCCESS, palletResponseStandardResult.getData());
//    }


    @Override
    public StandardResult internetTape(LogisticsProductManualRecordPO logisticsProductManualRecordPO) {
        ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(
            logisticsProductManualRecordPO.getMachineName(), logisticsProductManualRecordPO.getProcessCode());
        String erpOrderId = buildErpOrderId(logisticsProductManualRecordPO.getPlanTicketNo(),
            logisticsProductManualRecordPO.getProcessCode());

        LogisticsProductConfigDTO logisticsProductConfigDTO = new LogisticsProductConfigDTO();
        logisticsProductConfigDTO.setPlanTicketNo(logisticsProductManualRecordPO.getPlanTicketNo());
        logisticsProductConfigDTO.setMachineName(logisticsProductManualRecordPO.getMachineName());
        logisticsProductConfigDTO.setProcessCode(logisticsProductManualRecordPO.getProcessCode());

        JingShanLightBaseRequest<ApiCreatedPalletItemRequest> request =
            new JingShanLightBaseRequest<ApiCreatedPalletItemRequest>();
        ApiCreatedPalletItemRequest data = new ApiCreatedPalletItemRequest();

        data.setErpPalletId(logisticsProductManualRecordPO.getPalletCode());
        data.setCategoryId(logisticsProductManualRecordPO.getMaterialCategory());
        // 托 盘 状 态 ： GOOD=0 良 品 ， BAD=1 废 品 ，
        // RESTACKING=2 待整理，ABNORMAL=3 不正常
        data.setStatusTag(0);

        data.setPlaceIndex(0); // 默认0
        data.setPalletLength(BigDecimal.valueOf(logisticsProductManualRecordPO.getPalletLength()));
        data.setPalletWidth(BigDecimal.valueOf(logisticsProductManualRecordPO.getPalletWidth()));
        data.setPalletColumn(BigDecimal.ONE);

        data.setEntranceDeviceId(logisticsProductManualRecordPO.getMeshCode()); // 去机台管理中查询，第一个网待ID
        data.setCreateBy("MES");

        List<ApiCreatedPalletItemRequest.ApiCreatedPaperStackItem> apiCreatedPaperStackItems = Lists.newArrayList();
        ApiCreatedPalletItemRequest.ApiCreatedPaperStackItem apiCreatedPaperStackItem =
            new ApiCreatedPalletItemRequest.ApiCreatedPaperStackItem();
        apiCreatedPaperStackItem.setErpOrderId(erpOrderId);
        apiCreatedPaperStackItem.setEntranceDeviceId(logisticsProductManualRecordPO.getMeshCode());

        apiCreatedPaperStackItem.setCount(BigDecimal.valueOf(logisticsProductManualRecordPO.getPalletCount()));
        apiCreatedPaperStackItem.setStackLength(BigDecimal.valueOf(logisticsProductManualRecordPO.getStackLength()));
        apiCreatedPaperStackItem.setStackWidth(BigDecimal.valueOf(logisticsProductManualRecordPO.getStackWidth()));
        apiCreatedPaperStackItem.setStackColumn(BigDecimal.valueOf(logisticsProductManualRecordPO.getPalletColumns()));
        apiCreatedPaperStackItem.setOrderItem(new ApiCreatedPalletItemRequest.ApiCreatedOrderItem());
        apiCreatedPaperStackItem.getOrderItem().setErpOrderId(erpOrderId);
        apiCreatedPaperStackItem.getOrderItem().setErpWorkshopNo(logisticsProductManualRecordPO.getPlanTicketNo());

        // 查询订单信息
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(logisticsProductManualRecordPO.getPlanTicketNo());
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        // 获得客户集团信息
        PmaalTPO pmaalTPO = pmaalTService.getCustomerInfo(sfaaTVO.getSfaa009());
        apiCreatedPaperStackItem.getOrderItem().setProdDesc(logisticsProductManualRecordPO.getProductName());
        apiCreatedPaperStackItem.getOrderItem()
                .setProdDescShort(logisticsProductManualRecordPO.getProductShortName());
        apiCreatedPaperStackItem.getOrderItem().setRemarks("");
        apiCreatedPaperStackItem.getOrderItem().setCustID(sfaaTVO.getSfaa009());
        if (pmaalTPO != null) {
            apiCreatedPaperStackItem.getOrderItem().setCustName(pmaalTPO.getPmaal003());
        }
        apiCreatedPaperStackItem.getOrderItem()
                .setCustNameShort(logisticsProductManualRecordPO.getCustomerShortName());

        apiCreatedPaperStackItem.getOrderItem().setBoardQuality("");
        apiCreatedPaperStackItem.getOrderItem()
            .setSheetLength(BigDecimal.valueOf(logisticsProductManualRecordPO.getStackLength()));
        apiCreatedPaperStackItem.getOrderItem()
            .setSheetWidth(BigDecimal.valueOf(logisticsProductManualRecordPO.getStackWidth()));
        apiCreatedPaperStackItem.getOrderItem().setPlanCount(BigDecimal.valueOf(logisticsProductManualRecordPO.getPlanCount()));
        apiCreatedPaperStackItem.getOrderItem().setCategoryId(logisticsProductManualRecordPO.getMaterialCategory());
        // apiCreatedPaperStackItem.getOrderItem().setFlute("");

        apiCreatedPaperStackItems.add(apiCreatedPaperStackItem);
        data.setPaperStacks(apiCreatedPaperStackItems);

        request.setData(data);
        StandardResult<PalletResponse> palletResponseStandardResult =
            logisticsContext.get(machineConfig.getWorkshopCode()).internetTape(request);
        return StandardResult.resultCode(OperateCode.SUCCESS, palletResponseStandardResult.getData());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePalletInfo(LogisticsUpPalletInVO logisticsUpPalletInVO) {
        GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO = new GetLastPalletByPalletCodeInVO();
        getLastPalletByPalletCodeInVO.setPalletCode(logisticsUpPalletInVO.getPalletCode());
        getLastPalletByPalletCodeInVO.setProductTicketId(logisticsUpPalletInVO.getPlanTickId());

        ProductPalletPO productPalletPO = productLastPalletService.getByPalletCode(logisticsUpPalletInVO.getPalletCode());
        if (productPalletPO == null) {
            try {
                enterpriseWeChatService.robotSendMarkDownMessage(8, "机台[" + logisticsUpPalletInVO.getMachineName()
                    + "]物流自动上料[" + logisticsUpPalletInVO.getPalletCode() + "]不存在");
            } catch (Exception e){
                log.error("发送消息失败");
            }
            throw new CommonException("该栈板码"+ logisticsUpPalletInVO.getPalletCode() +"不存在");
        }

        SaveLastPalletInfoInVO saveLastPalletInfoInVO = new SaveLastPalletInfoInVO();
        saveLastPalletInfoInVO.setPalletCode(productPalletPO.getPalletCode());
        saveLastPalletInfoInVO.setPalletSource(productPalletPO.getPalletSource());
        saveLastPalletInfoInVO.setProductionOrder(productPalletPO.getProductionOrder());
        saveLastPalletInfoInVO.setProductionTime(productPalletPO.getProductionTime());
        saveLastPalletInfoInVO.setLoadingQuantity(productPalletPO.getRemainingQuantity());
        saveLastPalletInfoInVO.setConsumptionQuantity(productPalletPO.getConsumptionQuantity());
        saveLastPalletInfoInVO.setRemainingQuantity(productPalletPO.getRemainingQuantity());
        saveLastPalletInfoInVO.setUnit(productPalletPO.getUnit());
        saveLastPalletInfoInVO.setMachineName(logisticsUpPalletInVO.getMachineName());
        saveLastPalletInfoInVO.setProductTicketId(logisticsUpPalletInVO.getPlanTickId());
        productLastPalletService.saveIdePalletInfo(saveLastPalletInfoInVO,logisticsUpPalletInVO.getMeshType());

        // 给网带上 赋值栈板
        MeshByTypeAndMeshIdInVO meshByTypeAndMeshIdInVO = new MeshByTypeAndMeshIdInVO();
        meshByTypeAndMeshIdInVO.setWorkshopCode(logisticsUpPalletInVO.getWorkshopCode());
        meshByTypeAndMeshIdInVO.setMeshId(logisticsUpPalletInVO.getMeshId());
        List<Integer> meshTypes = Lists.newArrayList();
        meshTypes.add(logisticsUpPalletInVO.getMeshType());
        meshByTypeAndMeshIdInVO.setMeshTypes(meshTypes);
        MeshDTO mesh = meshService.getMeshByMeshId(meshByTypeAndMeshIdInVO);

        // 上料的话，需要把之前的备料给下料
        if (MeshTypeEnum.UP.getCode().equals(logisticsUpPalletInVO.getMeshType())) {
            MeshPO meshPO = meshService.getById(mesh.getId());
            // 把网带栈板下料
            ProductPalletPO palletCode = productLastPalletService.getByPalletCode(meshPO.getPalletCode());
            if (palletCode == null) {
                throw new CommonException("栈板码：" + logisticsUpPalletInVO.getPalletCode() + "不存在");
            }
            if (palletCode.getStatus().equals(Integer.valueOf(PalletOperateStatusEnum.IN_PRODUCTION.getCode()))) {
                log.info("栈板码：" + logisticsUpPalletInVO.getPalletCode() + "是在产状态，需要下料");
                DownMaterialInVO downMaterialInVO = new DownMaterialInVO();
                downMaterialInVO.setId(palletCode.getId());
                downMaterialInVO.setProductTicketId(logisticsUpPalletInVO.getPlanTickId());
                downMaterialInVO.setConsumptionQuantity(palletCode.getRemainingQuantity());
                downMaterialInVO.setRemainingQuantity(BigDecimal.ZERO);

                productLastPalletService.downPallet(downMaterialInVO);
            }

        }
        MeshPO meshPO = BeanUtil.copyProperties(mesh, MeshPO.class);
        meshPO.setPalletCode(logisticsUpPalletInVO.getPalletCode());
        meshService.updateById(meshPO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void outPalletInfo(LogisticsOutPalletInVO logisticsOutPalletInVO) {
        // 出站信息
        SaveOutboundInfoInVO outboundInfo = new SaveOutboundInfoInVO();
        outboundInfo.setMachineName(logisticsOutPalletInVO.getMachineName());
        outboundInfo.setProductTicketId(logisticsOutPalletInVO.getPlanTickId());
        outboundInfo.setPalletCode(logisticsOutPalletInVO.getPalletCode());
        // ids 返回的是他们的id
        outboundInfo.setIdsPalletId(logisticsOutPalletInVO.getPalletCode());
        outboundInfo.setProducedQuantity(logisticsOutPalletInVO.getProducedQuantity());
        outboundInfo.setUnit(logisticsOutPalletInVO.getUnit());
        outboundInfo.setIdsWorkOrderNo(logisticsOutPalletInVO.getIdsWorkOrderNo());
        
        // 校验
        ProductPalletPO productPalletPO = productLastPalletService.getByPalletCode(logisticsOutPalletInVO.getPalletCode());
        if (productPalletPO != null) {
            log.error("机台[" + logisticsOutPalletInVO.getMachineName()
                    + "]的栈板码[" + logisticsOutPalletInVO.getPalletCode() + "]已存在,出站失败");
            enterpriseWeChatService.robotSendMarkDownMessage(8, "机台[" + logisticsOutPalletInVO.getMachineName()
                + "]的栈板码[" + logisticsOutPalletInVO.getPalletCode() + "]已存在,出站失败");
            throw new CommonException("该栈板码" + logisticsOutPalletInVO.getPalletCode() + "已存在");
        }

        if (ObjectUtil.isNotNull(outboundInfo)) {
            String LOCK_KEY = RedisCacheConstant.STORAGE  + outboundInfo.getPalletCode();
            productOutboundService.saveOutboundInfo(LOCK_KEY,outboundInfo);


        }
    }



    @Override
    public StandardResult startPalletAutoCreator(String machineName,String processCode) {
        ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(machineName, processCode);
        if (Integer.valueOf(OutboundTypeEnum.SUBMIT.getCode()).equals(machineConfig.getOutboundType())
                || Integer.valueOf(OutboundTypeEnum.MANUAL.getCode()).equals(machineConfig.getOutboundType())
        ) {
            return StandardResult.resultCode(OperateCode.SUCCESS, true);
        }
        JingShanLightBaseRequest<String> request = new JingShanLightBaseRequest<String>();
        String deviceId = null;
        if (StringUtils.isNotBlank(machineConfig.getExitMeshCode())) {
            String[] split = machineConfig.getExitMeshCode().split(",");
            deviceId = split[0];
            if (deviceId.contains(":")) {
                deviceId = deviceId.split(":")[0];
            }
        }

        request.setData(deviceId);
        StandardResult<Boolean> palletResponseStandardResult =
        logisticsContext.get(machineConfig.getWorkshopCode()).startPalletAutoCreator(request);
        return StandardResult.resultCode(OperateCode.SUCCESS, palletResponseStandardResult.getData());
    }

    @Override
    public StandardResult pausePalletAutoCreator(String machineName, String processCode) {
        ProductionMachineLogisticsConfigPO machineConfig =
            productionMachineLogisticsConfigService.getMachineConfig(machineName, processCode);
        JingShanLightBaseRequest<String> request = new JingShanLightBaseRequest<String>();
        String deviceId = null;
        if (StringUtils.isNotBlank(machineConfig.getExitMeshCode())) {
            String[] split = machineConfig.getExitMeshCode().split(",");
            deviceId = split[0];
            if (deviceId.contains(":")) {
                deviceId = deviceId.split(":")[0];
            }
        }
        request.setData(deviceId);

        StandardResult<Boolean> palletResponseStandardResult =
            logisticsContext.get(machineConfig.getWorkshopCode()).pausePalletAutoCreator(request);
        return StandardResult.resultCode(OperateCode.SUCCESS, palletResponseStandardResult.getData());
    }

    @Override
    public StandardResult updatePalletAutoCreator(LogisticsProductConfigPO logisticsProductConfigPO,BigDecimal plannedProduct) {
        ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService
            .getMachineConfig(logisticsProductConfigPO.getMachineName(), logisticsProductConfigPO.getProcessCode());
        if (Integer.valueOf(OutboundTypeEnum.SUBMIT.getCode()).equals(machineConfig.getOutboundType())
                || Integer.valueOf(OutboundTypeEnum.MANUAL.getCode()).equals(machineConfig.getOutboundType())
        ) {
            return StandardResult.resultCode(OperateCode.SUCCESS, true);
        }
        String erpOrderId =
            buildErpOrderId(logisticsProductConfigPO.getPlanTicketNo(), logisticsProductConfigPO.getProcessCode());
        JingShanLightBaseRequest<PalletAutoCreatorRequset> request =
            new JingShanLightBaseRequest<PalletAutoCreatorRequset>();
        PalletAutoCreatorRequset data = new PalletAutoCreatorRequset();

        String deviceId = null;
        if (StringUtils.isNotBlank(machineConfig.getExitMeshCode())) {
            String[] split = machineConfig.getExitMeshCode().split(",");
            deviceId = split[0];
            if (deviceId.contains(":")) {
                deviceId = deviceId.split(":")[0];
            }
        }
        data.setMeshId(Integer.parseInt(deviceId)); // 去机台管理中查询，第一个网待ID
        data.setERPOrderId(erpOrderId);
        // data.setScheduleId("");
        // 查询订单信息
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(logisticsProductConfigPO.getPlanTicketNo());
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        // 获得客户集团信息
        PmaalTPO pmaalTPO = pmaalTService.getCustomerInfo(sfaaTVO.getSfaa009());
        ImaalTVO imaalTVO = imaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        if (imaalTVO != null) {
            data.setProdDesc(imaalTVO.getImaal003());
            data.setProdDescShort(imaalTVO.getImaal003());
        }
        // data.setRemarks("");
        data.setCustID(sfaaTVO.getSfaa009());
        if (pmaalTPO != null) {
            data.setCustName(pmaalTPO.getPmaal003());
            data.setCustNameShort(logisticsProductConfigPO.getCustomerShortName());
        }else {
            data.setCustName(" ");
            data.setCustNameShort(" ");
        }

        // data.setBoardQuality("");
        data.setStatus("");
        // data.setFlute("");
        data.setCategoryId(logisticsProductConfigPO.getMaterialCategory());
        // 物料类别名称
        if (StringUtil.equals(machineConfig.getWorkshopCode(), WorkShopEnum.HUSHI_WATERMARK.getCode())) {
            SysDictOutVO sysDictOutVO =
                sysDictService.listByTypeAndCode("S_PAPERBOARD_TYPE", logisticsProductConfigPO.getMaterialCategory());
            if (sysDictOutVO != null) {
                data.setCategoryName(sysDictOutVO.getDictValue());
            }
        } else if (StringUtil.equals(machineConfig.getWorkshopCode(), WorkShopEnum.HUSHI_OFFSET_PRINT.getCode())) {
            SysDictOutVO sysDictOutVO =
                sysDictService.listByTypeAndCode("J_PAPERBOARD_TYPE", logisticsProductConfigPO.getMaterialCategory());
            if (sysDictOutVO != null) {
                data.setCategoryName(sysDictOutVO.getDictValue());
            }
        }

        Integer planCount = plannedProduct.compareTo(BigDecimal.ZERO) == 0 ? 1 : plannedProduct.intValue();
        data.setPlanCount(planCount);
        // 规格长
        data.setSheetLength(Integer.parseInt(logisticsProductConfigPO.getStackLength().toString()));
        // 规格宽
        data.setSheetWidth(Integer.parseInt(logisticsProductConfigPO.getStackWidth().toString()));
        // 托盘列
        data.setPalletColumn(Integer.parseInt(logisticsProductConfigPO.getPalletColumns().toString()));
        // 托盘长
        data.setPalletLength(Integer.parseInt(logisticsProductConfigPO.getPalletLength().toString()));
        // 托盘宽
        data.setPalletWidth(Integer.parseInt(logisticsProductConfigPO.getPalletWidth().toString()));
        // 托盘数量
        data.setPalletCount(logisticsProductConfigPO.getPalletCount());
        // 自动创建间隔时间
        // data.setAutoCreateInterval(1);
        // 自动创建
        data.setAutoCreate(true);
        // 间隔计数器
        // data.setIntervalCounter(1);
        // 托盘角度
        // data.setPalletAngle(1);
        // 托盘类型
        // data.setPalletType(1);
        // 提示信息
        // data.setMessage("");

        request.setData(data);

        StandardResult<Boolean> palletResponseStandardResult =
                logisticsContext.get(machineConfig.getWorkshopCode()).updatePalletAutoCreator(request);
        return StandardResult.resultCode(OperateCode.SUCCESS, palletResponseStandardResult.getData());
    }

    public static boolean acquireLock(String lockKey) {
        // 使用Redis的SET命令实现分布式锁，并设置过期时间
        Boolean lockAcquired = RedisUtil.getLock(lockKey, "locked", 10, TimeUnit.SECONDS);
        return lockAcquired != null && lockAcquired;
    }

    public static void releaseLock(String lockKey) {
        // 释放锁
        RedisUtil.delete(lockKey);
    }



}
