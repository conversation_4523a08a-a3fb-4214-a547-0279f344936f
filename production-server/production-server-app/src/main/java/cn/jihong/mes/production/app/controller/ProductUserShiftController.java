package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mes.production.api.service.IProductUserShiftService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 员工班组信息表
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@RestController
@RequestMapping("/productUserShift")
@ShenyuSpringMvcClient(path = "/productUserShift/**")
public class ProductUserShiftController {

    @Resource
    private IProductUserShiftService productUserShiftService;

    /**
     * 保存班组信息
     */
    @PostMapping("/saveProductShift")
    public StandardResult saveProductShift(@RequestBody @Valid ProductShiftInVO productShiftInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productUserShiftService.saveProductShift(productShiftInVO));
    }

    /**
     * 查询班组信息
     */
    @GetMapping("/getProductShift")
    public StandardResult<ProductShiftInVO> getProductShift() {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productUserShiftService.getProductShift());
    }

    /**
     * 更新班组信息
     */
    @PostMapping("/updateProductShift")
    public StandardResult updateProductShift(@RequestBody ProductShiftInVO productShiftInVO) {
        productUserShiftService.updateProductShift(productShiftInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

}

