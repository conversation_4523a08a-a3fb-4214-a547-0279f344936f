package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.common.convertor.PageConvertor;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.production.api.model.constant.SequenceConst;
import cn.jihong.mes.production.api.model.dto.ProductMaterialCountDTO;
import cn.jihong.mes.production.api.model.dto.ProductPalletOperationRecordsDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.enums.*;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.config.JinshanLogisticsConfig;
import cn.jihong.mes.production.app.mapper.ProductLastPalletMapper;
import cn.jihong.mes.production.app.service.productVerify.ProductPalletServer;
import cn.jihong.mes.production.app.util.redis.OrderSequenceUtil;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.tms.api.model.enums.TicketTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 上栈板信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@Service("productLastPalletServiceImpl")
public class ProductLastPalletServiceImpl extends JiHongServiceImpl<ProductLastPalletMapper, ProductPalletPO>
    implements IProductLastPalletService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductOutboundService productOutboundService;
    @DubboReference
    private IA01Service ia01Service;



    @Resource
    private IProductPalletOperationRecordsService productPalletOperationRecordsService;

    @Resource
    private IProductMaterialCountService ProductMaterialCountService;
    @Resource
    private JinshanLogisticsConfig jinshanLogisticsConfig;
    @DubboReference
    private IProductionMachineLogisticsConfigService productionMachineLogisticsConfigService;
    @Resource
    private ProductPalletServer productPalletServer;
    @Resource
    private IProductLastPalletService productLastPalletService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveLastPalletInfo(SaveLastPalletInfoInVO saveLastPalletInfoInVO) {
        if (BigDecimal.ZERO.compareTo(saveLastPalletInfoInVO.getLoadingQuantity()) >= 0) {
            throw new CommonException("上料数量不能小于等于0");
        }

        ProductTicketPO productTicketPO = productTicketService.verify(saveLastPalletInfoInVO.getProductTicketId());

        GetByMachineNameInVO getByMachineNameInVO = new GetByMachineNameInVO();
        getByMachineNameInVO.setMachineName(saveLastPalletInfoInVO.getMachineName());
        List<LastPalletOutVO> lastPalletList = getStagingLastPalletListByMachineName(getByMachineNameInVO);
        if (CollectionUtil.isNotEmpty(lastPalletList)) {
            String palletCode = lastPalletList.stream().map(LastPalletOutVO::getPalletCode).collect(Collectors.joining(","));
            throw new CommonException("存在暂存的栈板" + palletCode + ",请先在转产中上栈板");
        }

        // 校验  栈板码只能使用一次
        verify(saveLastPalletInfoInVO.getPalletCode());

        // 校验，只能上当前的生产计划工程单的栈板
//        if (!verifyTicket(saveLastPalletInfoInVO)) {
//            throw new CommonException("工程单号不一致");
//        }
        //  productTicketService.updateMachineStopNo(saveLastPalletInfoInVO.getProductTicketId(), saveLastPalletInfoInVO.getMachineStopNo());
        // 更新之前的数据  -- 默认下栈板操作
        ProductPalletPO productLastPalletPOOld =
                updateProductLastPalletPO(saveLastPalletInfoInVO, productTicketPO);
        if (productLastPalletPOOld != null) {
            // 保存栈板操作记录 -- 下栈板记录
            savePalletOperationRecords(productLastPalletPOOld.getLoadingQuantity(),
                    productLastPalletPOOld.getConsumptionQuantity(), productLastPalletPOOld.getRemainingQuantity(),
                    PalletOperateTypeEnum.DOWN_PALLET.getCode(), productTicketPO, productLastPalletPOOld.getId(),
                    productLastPalletPOOld.getPalletCode(), saveLastPalletInfoInVO.getMachineStopNo(),
                    saveLastPalletInfoInVO.getRemainingReason());
        }


        // 保存新的栈板信息  -- 保存或更新当前栈板信息
        ProductPalletPO productPalletPO =
                getProductLastPalletPO(saveLastPalletInfoInVO, productTicketPO, Integer.valueOf(PalletOperateStatusEnum.IN_PRODUCTION.getCode()));

        // 保存栈板操作记录 -- 上栈板记录
        savePalletOperationRecords(productPalletPO.getLoadingQuantity(),
                BigDecimal.ZERO, productPalletPO.getRemainingQuantity(),
                PalletOperateTypeEnum.UP_PALLET.getCode(), productTicketPO, productPalletPO.getId(),
                productPalletPO.getPalletCode(), saveLastPalletInfoInVO.getMachineStopNo(), null);

        return String.valueOf(productPalletPO.getId());
    }

    @Override
    public String saveIdePalletInfo(SaveLastPalletInfoInVO saveLastPalletInfoInVO, Integer meshType) {
        if (BigDecimal.ZERO.compareTo(saveLastPalletInfoInVO.getLoadingQuantity()) >= 0) {
            throw new CommonException("上料数量不能小于等于0");
        }

        ProductTicketPO productTicketPO = productTicketService.verify(saveLastPalletInfoInVO.getProductTicketId());

        // 校验  栈板码只能使用一次
        verify(saveLastPalletInfoInVO.getPalletCode());

        // 保存新的栈板信息  -- 保存或更新当前栈板信息
        ProductPalletPO productPalletPO = new ProductPalletPO();
        if (meshType.equals(MeshTypeEnum.PREPARE.getCode())) {
            productPalletPO = getProductLastPalletPO(saveLastPalletInfoInVO, productTicketPO,Integer.valueOf(PalletOperateStatusEnum.PREPARE.getCode()));
        } else if (meshType.equals(MeshTypeEnum.UP.getCode())) {
            productPalletPO = getProductLastPalletPO(saveLastPalletInfoInVO, productTicketPO,Integer.valueOf(PalletOperateStatusEnum.IN_PRODUCTION.getCode()));
        }


        // 保存栈板操作记录 -- 上栈板记录
        if (meshType.equals(MeshTypeEnum.UP.getCode())) {
            savePalletOperationRecords(productPalletPO.getLoadingQuantity(),
                    BigDecimal.ZERO, productPalletPO.getRemainingQuantity(),
                    PalletOperateTypeEnum.UP_PALLET.getCode(), productTicketPO, productPalletPO.getId(),
                    productPalletPO.getPalletCode(), saveLastPalletInfoInVO.getMachineStopNo(),null);
        } else if (meshType.equals(MeshTypeEnum.PREPARE.getCode())) {
            savePalletOperationRecords(productPalletPO.getLoadingQuantity(),
                    BigDecimal.ZERO, productPalletPO.getRemainingQuantity(),
                    PalletOperateTypeEnum.PREPARE.getCode(), productTicketPO, productPalletPO.getId(),
                    productPalletPO.getPalletCode(), saveLastPalletInfoInVO.getMachineStopNo(),null);
        }
        return String.valueOf(productPalletPO.getId());
    }


    private void verify(String palletCode) {
        // 存在一条在库，且不能存在在产
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductPalletPO::getPalletCode, palletCode);
        ProductPalletPO productPalletPO = getOne(lambdaQueryWrapper);
        if (productPalletPO == null) {
            throw new CommonException("栈板码：" + palletCode + "不存在");
        }
        if (productPalletPO.getStatus().equals(PalletOperateStatusEnum.IN_PRODUCTION.getCode())) {
            throw new CommonException("栈板码：" + palletCode + "是在产状态，不可重复上栈板");
        }
    }

    @Override
    public void savePalletOperationRecords(BigDecimal originalQuantity, BigDecimal consumptionQuantity,
                                           BigDecimal remainingQuantity, String operationType, ProductTicketPO productTicketPO,
                                           Long productPalletId, String palletCode, Integer machineStopNo,String remainingReason) {
        ProductPalletOperationRecordsDTO productPalletOperationRecordsDTO = new ProductPalletOperationRecordsDTO();
        productPalletOperationRecordsDTO.setOperationType(operationType);
        productPalletOperationRecordsDTO.setTicketType(String.valueOf(TicketTypeEnum.PRODUCTION.getCode()));
        productPalletOperationRecordsDTO.setTicketNumber(productTicketPO.getTicketRequestId());
        productPalletOperationRecordsDTO.setOperator(productTicketPO.getProcessorId());
        productPalletOperationRecordsDTO.setOriginalQuantity(originalQuantity);
        productPalletOperationRecordsDTO.setConsumptionQuantity(consumptionQuantity);
        productPalletOperationRecordsDTO.setRemainingQuantity(remainingQuantity);
        productPalletOperationRecordsDTO.setProductTicketId(productTicketPO.getId());
        productPalletOperationRecordsDTO.setProductPalletId(productPalletId);
        productPalletOperationRecordsDTO.setPalletCode(palletCode);
        productPalletOperationRecordsDTO.setMachineStopNo(machineStopNo);
        productPalletOperationRecordsDTO.setCreateBy(SecurityUtil.getUserId());
        productPalletOperationRecordsDTO.setCompanyCode(productTicketPO.getCompanyCode());
        productPalletOperationRecordsService.work(productPalletOperationRecordsDTO,operationType);
    }

    @Override
    public Pagination<PalletUseOutVO> getPalletUseList(GetPalletListPageInVO getPalletListPageInVO) {
        if (StringUtils.isBlank(getPalletListPageInVO.getCompanyCode())) {
            getPalletListPageInVO.setCompanyCode(SecurityUtil.getCompanySite());
        }
        Page<PalletUseOutVO> page = baseMapper.getPalletUseList(getPalletListPageInVO.getPage(),getPalletListPageInVO);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page.getRecords().stream().map(r->{
//            r.setPalletSourceName();
            r.setStatusName(MaterialEnum.getMaterialEnum(Integer.valueOf(r.getStatus())).getName());
//            r.setCompanyCodeName();
            return r;
        }).collect(Collectors.toList()), page);
    }

    @Override
    public PalletUseOutVO getPalletUseDetial(Long id) {
        return baseMapper.getPalletUseDetial(id);
    }

    @Override
    public Pagination<PalletUseRecordOutVO> getPalletUseDetialList(ProductInfoPageInVO productInfoPageInVO) {
        Page<PalletUseRecordOutVO> page = baseMapper.getPalletUseDetialList(productInfoPageInVO.getPage(),productInfoPageInVO);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null);
        }
        List<Long> userIds =
                page.getRecords().stream().map(PalletUseRecordOutVO::getCreateBy).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
        return Pagination.newInstance(page.getRecords().stream().map(r->{
            if (r.getCreateBy() != null) {
                r.setCreaterName(userMap.get(r.getCreateBy()));
            }
            r.setOperationTypeName(PalletOperateTypeEnum.getPalletOperateTypeEnum(r.getOperationType()).getName());
            r.setTicketTypeName(TicketTypeEnum.getTicketTypeEnum(Integer.valueOf(r.getTicketType())).getCnName());
            return r;
        }).collect(Collectors.toList()), page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long downPallet(DownMaterialInVO downMaterialInVO) {
        if (jinshanLogisticsConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite())) {
            return downMesPallet(downMaterialInVO);
        } else {
            return downMesPallet(downMaterialInVO);
        }
    }


    private Long downMesPallet(DownMaterialInVO downMaterialInVO) {
        ProductPalletPO productPalletPO = getById(downMaterialInVO.getId());
        if (productPalletPO == null) {
            throw new CommonException("栈板不存在");
        }
        if (Integer.valueOf(PalletOperateStatusEnum.IN_LIBRARY.getCode()).equals(productPalletPO.getStatus())) {
            throw new CommonException(productPalletPO.getPalletCode() + "栈板是在库状态，无法下料，请刷新页面后操作");
        }

        // 剩余数量不能大于实际的剩余数量
        if (downMaterialInVO.getRemainingQuantity().compareTo(productPalletPO.getRemainingQuantity()) == 1) {
            throw new CommonException("剩余数量" + downMaterialInVO.getRemainingQuantity() + "不得大于实际的剩余数量"
                    + productPalletPO.getRemainingQuantity());
        }
        // 消耗数量 不得大于 剩余数量
        if (downMaterialInVO.getConsumptionQuantity()
                .compareTo(productPalletPO.getRemainingQuantity()) == 1) {
            throw new CommonException("消耗数量" + downMaterialInVO.getConsumptionQuantity() + "不得大于实际的剩余数量"
                    + productPalletPO.getRemainingQuantity());
        }
        // 领用数量不等于消耗数量+剩余数量
        if (!(downMaterialInVO.getConsumptionQuantity().add(downMaterialInVO.getRemainingQuantity())
                .compareTo(productPalletPO.getLoadingQuantity()) == 0)) {
            throw new CommonException("消耗数量" + downMaterialInVO.getConsumptionQuantity() + " + 剩余数量"
                    + downMaterialInVO.getRemainingQuantity() + " = "
                    + downMaterialInVO.getConsumptionQuantity().add(downMaterialInVO.getRemainingQuantity())
                    + "不等于领用数量" + productPalletPO.getLoadingQuantity());
        }
        ProductPalletPO palletPO = new ProductPalletPO();
        palletPO.setId(downMaterialInVO.getId());
        palletPO.setConsumptionQuantity(downMaterialInVO.getConsumptionQuantity());
        palletPO.setRemainingQuantity(downMaterialInVO.getRemainingQuantity());
        palletPO.setRemainingReason(downMaterialInVO.getRemainingReason());
        palletPO.setStatus(Integer.valueOf(PalletOperateStatusEnum.IN_LIBRARY.getCode()));
        updateById(palletPO);

        ProductTicketPO productTicketPO = productTicketService.getById(downMaterialInVO.getProductTicketId() != null
                ? downMaterialInVO.getProductTicketId() : productPalletPO.getRequisitionTicketId());
        // 保存栈板操作记录
        savePalletOperationRecords(productPalletPO.getLoadingQuantity(),
                downMaterialInVO.getConsumptionQuantity(), downMaterialInVO.getRemainingQuantity(),
                PalletOperateTypeEnum.DOWN_PALLET.getCode(), productTicketPO, downMaterialInVO.getId(),
                productPalletPO.getPalletCode(), downMaterialInVO.getMachineStopNo(),
                downMaterialInVO.getRemainingReason());

        return downMaterialInVO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long temporaryStoragePallet(TemporaryStoragePalletInVO inVO) {
        ProductPalletPO productPalletPO = getById(inVO.getId());
        if (productPalletPO == null) {
            throw new CommonException("栈板不存在");
        }
        if (PalletOperateStatusEnum.TEMPORARY_STORAGE.getCode().equals(productPalletPO.getStatus())) {
            throw new CommonException("栈板已暂存，请勿重复操作");
        }

        // 剩余数量不能大于实际的剩余数量
        if (inVO.getRemainingQuantity().compareTo(productPalletPO.getRemainingQuantity()) == 1) {
            throw new CommonException("剩余数量" + inVO.getRemainingQuantity() + "不得大于实际的剩余数量"
                    + productPalletPO.getRemainingQuantity());
        }
        // 消耗数量 不得大于 剩余数量
        if (inVO.getConsumptionQuantity()
                .compareTo(productPalletPO.getRemainingQuantity()) == 1) {
            throw new CommonException("消耗数量" + inVO.getConsumptionQuantity() + "不得大于实际的剩余数量"
                    + productPalletPO.getRemainingQuantity());
        }
        // 领用数量不等于消耗数量+剩余数量
        if (!(inVO.getConsumptionQuantity().add(inVO.getRemainingQuantity())
                .compareTo(productPalletPO.getLoadingQuantity()) == 0)) {
            throw new CommonException("消耗数量" + inVO.getConsumptionQuantity() + " + 剩余数量"
                    + inVO.getRemainingQuantity() + " = "
                    + inVO.getConsumptionQuantity().add(inVO.getRemainingQuantity())
                    + "不等于领用数量" + productPalletPO.getLoadingQuantity());
        }
        ProductPalletPO palletPO = new ProductPalletPO();
        palletPO.setId(inVO.getId());
        palletPO.setConsumptionQuantity(inVO.getConsumptionQuantity());
        palletPO.setRemainingQuantity(inVO.getRemainingQuantity());
        palletPO.setRemainingReason(inVO.getRemainingReason());
        palletPO.setStatus(PalletOperateStatusEnum.TEMPORARY_STORAGE.getCode());
        updateById(palletPO);


        ProductTicketPO productTicketPO = productTicketService.getById(productPalletPO.getProductTicketId());
        // 保存栈板操作记录
        savePalletOperationRecords(productPalletPO.getLoadingQuantity(),
                inVO.getConsumptionQuantity(), inVO.getRemainingQuantity(),
                PalletOperateTypeEnum.TEMPORARY_STORAGE.getCode(), productTicketPO, inVO.getId(),
                productPalletPO.getPalletCode(), inVO.getMachineStopNo(),
                inVO.getRemainingReason());

        return inVO.getId();
    }

    @Override
    public List<LastPalletOutVO> getLastPalletListByMachineName(GetByMachineNameInVO getByMachineNameInVO) {
        // 暂存 + 在产
        List<Integer> status = new ArrayList<>();
        status.add(PalletOperateStatusEnum.IN_PRODUCTION.getCode());
        status.add(PalletOperateStatusEnum.PREPARE.getCode());
        return getLastPalletListByMachineName(getByMachineNameInVO,status);
    }

    @Override
    public ProductPalletPO getByPalletCode(String palletCode) {
        LambdaQueryWrapper<ProductPalletPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductPalletPO::getPalletCode, palletCode);
        return getOne(queryWrapper);
    }

    @Override
    public List<LastPalletOutVO> getLastPalletListByMachineName(GetByMachineNameInVO getByMachineNameInVO, List<Integer> status) {
        LambdaQueryWrapper<ProductPalletPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductPalletPO::getRequisitionMachineName, getByMachineNameInVO.getMachineName())
                .in(ProductPalletPO::getStatus, status);
        List<ProductPalletPO> productLastPalletPOS = list(queryWrapper);
        List<LastPalletOutVO> lastPalletOutVOS = BeanUtil.copyToList(productLastPalletPOS, LastPalletOutVO.class);

        if (CollectionUtil.isEmpty(lastPalletOutVOS)) {
            return lastPalletOutVOS;
        }

        // 封装字段
        lastPalletOutVOS.stream().forEach(lastPalletRecordsOutVO -> {
            lastPalletRecordsOutVO.setStatusName(PalletOperateStatusEnum.getPalletOperateStatusEnum(lastPalletRecordsOutVO.getStatus()).getName());
        });
        return lastPalletOutVOS;
    }

    /**
     * 查询暂存中的上栈板列表
     * @param getByMachineNameInVO
     * @return
     */
    @Override
    public List<LastPalletOutVO> getStagingLastPalletListByMachineName(GetByMachineNameInVO getByMachineNameInVO) {
        return getLastPalletListByMachineName(getByMachineNameInVO,
            Arrays.asList(PalletOperateStatusEnum.TEMPORARY_STORAGE.getCode()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long updatePalletStatus(UpdatePalletStatusInVO updatePalletStatusInVO) {
        ProductPalletPO productPalletPO = getById(updatePalletStatusInVO.getId());
        ProductTicketPO productTicketPO = productTicketService.getById(updatePalletStatusInVO.getProductTicketId());
        if (productPalletPO == null) {
            throw new CommonException("栈板不存在");
        }
        if (!productPalletPO.getStatus().equals(PalletOperateStatusEnum.TEMPORARY_STORAGE.getCode())) {
            throw new CommonException("栈板已转产,请勿重复操作");
        }

        Integer status = null;
        // 判断类型
        if (PalletOperateTypeEnum.UP_PALLET.getCode().equals(updatePalletStatusInVO.getOperation())) {
            status = PalletOperateStatusEnum.IN_PRODUCTION.getCode();
            // 新的上料数量等于旧的剩余数量
            productPalletPO.setLoadingQuantity(productPalletPO.getRemainingQuantity());
        }
        if (PalletOperateTypeEnum.DOWN_PALLET.getCode().equals(updatePalletStatusInVO.getOperation())) {
            status = PalletOperateStatusEnum.IN_LIBRARY.getCode();
        }

        productPalletPO.setStatus(status);
        updateById(productPalletPO);

        // 保存栈板操作记录
        savePalletOperationRecords(productPalletPO.getRemainingQuantity(),
                BigDecimal.ZERO, productPalletPO.getRemainingQuantity(),
                updatePalletStatusInVO.getOperation(), productTicketPO, productPalletPO.getId(),
                productPalletPO.getPalletCode(), productPalletPO.getMachineStopNo(),
                productPalletPO.getRemainingReason());

        return productPalletPO.getId();
    }

    @Override
    public List<ProductPalletPO> getByProductTicketIds(List<Long> productTicketIds) {
        if (CollectionUtil.isEmpty(productTicketIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        // 领用工单id
        lambdaQueryWrapper.in(ProductPalletPO::getRequisitionTicketId,productTicketIds);
        return list(lambdaQueryWrapper);
    }

    @Override
    public ProductPalletPO getByOutboundId(Long outboundId) {
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductPalletPO::getOutboundId,outboundId);
        return getOne(lambdaQueryWrapper);
    }



    @Override
    public String getPalletShortCode(Long id) {
        ProductTicketPO productTicketPO = productTicketService.getById(id);
        Long sequence = OrderSequenceUtil.getAndIncrementOrderSequence(
            SequenceConst.CREATE_SHORT_PALLET_ORDER_KEY + ":" + productTicketPO.getPlanTicketNo(),
            SequenceConst.CREATE_SHORT_PALLET_LOCK_KEY + ":" + productTicketPO.getPlanTicketNo());
        String palletShortCode = String.format("%06d", sequence);
        return palletShortCode;
    }

    @Override
    public GetLastPalletByPalletCodeOutVO getPalletByPalletShortCode(GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO) {
        if (StringUtils.isBlank(getLastPalletByPalletCodeInVO.getPalletShortCode())){
            throw new CommonException("栈板短码不能为空");
        }
        ProductTicketPO productTicketPO = productTicketService.getById(getLastPalletByPalletCodeInVO.getProductTicketId());
        // 上栈板出站信息
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductPalletPO::getPalletShortCode, getLastPalletByPalletCodeInVO.getPalletShortCode())
                .eq(ProductPalletPO::getPlanTicketNo,StringUtils.isNotBlank(getLastPalletByPalletCodeInVO.getPlanTicketNo())
                        ? getLastPalletByPalletCodeInVO.getPlanTicketNo() : productTicketPO.getPlanTicketNo())
                .orderByDesc(ProductPalletPO::getUpdateTime);
        List<ProductPalletPO> productPalletPOS = list(lambdaQueryWrapper);

        if (CollectionUtil.isEmpty(productPalletPOS)) {
            GetLastPalletByPalletCodeOutVO getLastPalletByPalletCodeOutVO = new GetLastPalletByPalletCodeOutVO();
            getLastPalletByPalletCodeOutVO.setPalletShortCodeStatus(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            getLastPalletByPalletCodeOutVO.setPalletShortCodeMessage("当前工单["+productTicketPO.getPlanTicketNo()+"]不存在栈板短码[" + getLastPalletByPalletCodeInVO.getPalletCode() + "]信息，请指定工单号查询");
            return getLastPalletByPalletCodeOutVO;
        }

        // 获得最新的一条
        ProductPalletPO productPalletPO = productPalletPOS.get(0);
        // 赋值会这个对象 兼容旧代码
        getLastPalletByPalletCodeInVO.setPalletCode(productPalletPO.getPalletCode());

        return getLastPalletByPalletCodeOutVO(productPalletPO,productTicketPO);
    }

    @Override
    public Pagination<LastPalletRecordsOutVO> getDownPalletRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<LastPalletRecordsOutVO> iPage = productPalletOperationRecordsService
            .getDownPalletRecords(productTicketPageInVO.getPage(), productTicketPageInVO.getProductTicketId());

        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        // 封装字段
        iPage.getRecords().stream().forEach(lastPalletRecordsOutVO -> {
            lastPalletRecordsOutVO.setStatusName(
                PalletOperateStatusEnum.getPalletOperateStatusEnum(lastPalletRecordsOutVO.getStatus()).getName());
        });
        return getLastPalletOutVOPagination(iPage, iPage);
    }


    private GetLastPalletByPalletCodeOutVO getLastPalletByPalletCodeOutVO(ProductPalletPO productPalletPO,ProductTicketPO productTicketPO) {
        GetLastPalletByPalletCodeOutVO lastPalletOutVO = new GetLastPalletByPalletCodeOutVO();
        // 查询是否有相同工序的栈板存在
        hasOldPallet(lastPalletOutVO,productTicketPO.getMachineName(),productPalletPO,productTicketPO);

        lastPalletOutVO.setPalletCode(productPalletPO.getPalletCode());
        lastPalletOutVO.setPalletSource(productPalletPO.getPalletSource());
        lastPalletOutVO.setProductionOrder(productPalletPO.getProductionOrder());
        lastPalletOutVO.setProductionTime(productPalletPO.getCreateTime());
        // 设置上料时间
        lastPalletOutVO.setLoadingTime(new java.util.Date());
        lastPalletOutVO.setRemainingQuantity(productPalletPO.getRemainingQuantity());
        lastPalletOutVO.setLoadingQuantity(productPalletPO.getRemainingQuantity());
        lastPalletOutVO.setUnit(productPalletPO.getUnit());
        lastPalletOutVO.setUnitName(UnitEnum.getUnitEnum(productPalletPO.getUnit()).getName());
        lastPalletOutVO.setMachineName(productPalletPO.getMachineName());

        // 默认短码状态是1
        lastPalletOutVO.setPalletShortCodeStatus(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        lastPalletOutVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        return lastPalletOutVO;
    }


    /**
     * 将之前相同工序的栈板给下栈板操作
     * @param saveLastPalletInfoInVO
     */
    private ProductPalletPO updateProductLastPalletPO(SaveLastPalletInfoInVO saveLastPalletInfoInVO,ProductTicketPO productTicketPO) {
        ProductMaterialCountDTO ProductMaterialCount = ProductMaterialCountService
            .getPalletCountCount(productTicketPO.getProcessType(), saveLastPalletInfoInVO.getPalletSource());

        // 比较现有栈板数量和原来数量的
        if (ObjectUtil.isNotNull(ProductMaterialCount)) {
            List<ProductPalletPO> productPalletPOS = getByMachineNameAndSource(saveLastPalletInfoInVO.getMachineName(),saveLastPalletInfoInVO.getPalletSource());
            if (ProductMaterialCount.getPalletCount() <= productPalletPOS.size()) {
                throw new CommonException(
                    "机台的上道工序"+ saveLastPalletInfoInVO.getPalletSource() +"可上栈板数量为：" + ProductMaterialCount.getPalletCount() + "，已上栈板" + productPalletPOS.size());
            }
            return null;
        } else {
            // 自动换栈板操作
            LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductPalletPO::getRequisitionMachineName, saveLastPalletInfoInVO.getMachineName())
                .eq(ProductPalletPO::getStatus, Integer.valueOf(PalletOperateStatusEnum.IN_PRODUCTION.getCode()))
                .eq(ProductPalletPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()))
                .eq(ProductPalletPO::getPalletSource, saveLastPalletInfoInVO.getPalletSource());
            ProductPalletPO productPalletPO = getOne(lambdaQueryWrapper);
            if (productPalletPO == null) {
                return null;
            }

            // 剩余数量不能大于实际的剩余数量
            if (saveLastPalletInfoInVO.getRemainingQuantity().compareTo(productPalletPO.getRemainingQuantity()) == 1) {
                throw new CommonException("剩余数量" + saveLastPalletInfoInVO.getRemainingQuantity() + "不得大于实际的剩余数量"
                    + productPalletPO.getRemainingQuantity());
            }
            // 消耗数量 不得大于 剩余数量
            if (saveLastPalletInfoInVO.getConsumptionQuantity()
                .compareTo(productPalletPO.getRemainingQuantity()) == 1) {
                throw new CommonException("消耗数量" + saveLastPalletInfoInVO.getConsumptionQuantity() + "不得大于实际的剩余数量"
                    + productPalletPO.getRemainingQuantity());
            }
            // 领用数量不等于消耗数量+剩余数量
            if (!(saveLastPalletInfoInVO.getConsumptionQuantity().add(saveLastPalletInfoInVO.getRemainingQuantity())
                .compareTo(productPalletPO.getLoadingQuantity()) == 0)) {
                throw new CommonException("消耗数量" + saveLastPalletInfoInVO.getConsumptionQuantity() + " + 剩余数量"
                    + saveLastPalletInfoInVO.getRemainingQuantity() + " = "
                    + saveLastPalletInfoInVO.getConsumptionQuantity().add(saveLastPalletInfoInVO.getRemainingQuantity())
                    + "不等于领用数量" + productPalletPO.getLoadingQuantity());
            }

            productPalletPO.setStatus(Integer.valueOf(PalletOperateStatusEnum.IN_LIBRARY.getCode()));
            productPalletPO.setConsumptionQuantity(saveLastPalletInfoInVO.getConsumptionQuantity());
            productPalletPO.setRemainingQuantity(saveLastPalletInfoInVO.getRemainingQuantity());
            productPalletPO.setRemainingReason(saveLastPalletInfoInVO.getRemainingReason());
            productPalletPO.setMachineStopNo(saveLastPalletInfoInVO.getMachineStopNo());
            updateById(productPalletPO);

            return productPalletPO;
        }
    }

    private List<ProductPalletPO> getByMachineNameAndSource(String machineName,String palletSource) {
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductPalletPO::getRequisitionMachineName,machineName)
                .eq(ProductPalletPO::getPalletSource,palletSource)
                .eq(ProductPalletPO::getStatus, Integer.valueOf(PalletOperateStatusEnum.IN_PRODUCTION.getCode()))
                .eq(ProductPalletPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        List<ProductPalletPO> productPalletPOS = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(productPalletPOS)) {
            return productPalletPOS;
        }
        return Lists.newArrayList();
    }

    private ProductPalletPO getProductLastPalletPO(SaveLastPalletInfoInVO saveLastPalletInfoInVO,
        ProductTicketPO productTicketPO,Integer status) {
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductPalletPO::getPalletCode, saveLastPalletInfoInVO.getPalletCode());
        ProductPalletPO productPalletPO = getOne(lambdaQueryWrapper);
        if (productPalletPO == null) {
            productPalletPO = new ProductPalletPO();
            productPalletPO.setMachineName(saveLastPalletInfoInVO.getMachineName());
            productPalletPO.setPalletSource(saveLastPalletInfoInVO.getPalletSource());
            productPalletPO.setPalletCode(saveLastPalletInfoInVO.getPalletCode());
            productPalletPO.setUnit(saveLastPalletInfoInVO.getUnit());
            productPalletPO.setCreateBy(SecurityUtil.getUserId());
        }
        productPalletPO.setRequisitionTicketId(saveLastPalletInfoInVO.getProductTicketId());
        productPalletPO.setRequisitionOrder(productTicketPO.getPlanTicketNo());
        productPalletPO.setRequisitionMachineName(saveLastPalletInfoInVO.getMachineName());
        productPalletPO.setLoadingQuantity(saveLastPalletInfoInVO.getLoadingQuantity());
        productPalletPO.setStatus(status);
        productPalletPO.setCompanyCode(productTicketPO.getCompanyCode());

        productPalletPO.setCompanyCode(productTicketPO.getCompanyCode());

        // 设置上料时间
        productPalletPO.setLoadingTime(new java.util.Date());

        saveOrUpdate(productPalletPO);
        return productPalletPO;
    }


    /**
     * 根据栈板码查询上栈板信息
     */
    @Override
    public GetLastPalletByPalletCodeOutVO getLastPalletByPalletCode(GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO) {
        ProductTicketPO productTicketPO = productTicketService.getById(getLastPalletByPalletCodeInVO.getProductTicketId());
        // 上栈板出站信息
        LambdaQueryWrapper<ProductPalletPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductPalletPO::getPalletCode, getLastPalletByPalletCodeInVO.getPalletCode())
                .orderByDesc(ProductPalletPO::getUpdateTime);
        List<ProductPalletPO> productPalletPOS = list(lambdaQueryWrapper);

        if (CollectionUtil.isEmpty(productPalletPOS)) {
            throw new CommonException("栈板码：" + getLastPalletByPalletCodeInVO.getPalletCode() + "不存在对应的栈板信息");
        }

        // 获得最新的一条
        ProductPalletPO productPalletPO = productPalletPOS.get(0);

        return getLastPalletByPalletCodeOutVO(productPalletPO,productTicketPO);
    }

    /**
     * 查询是否有相同工序的栈板存在
     * @param lastPalletOutVO
     * @param machineName
     * @param productPalletPO
     */
    private void hasOldPallet(GetLastPalletByPalletCodeOutVO lastPalletOutVO, String machineName, ProductPalletPO productPalletPO,ProductTicketPO productTicketPO) {
        ProductMaterialCountDTO ProductMaterialCount = ProductMaterialCountService.getPalletCountCount(productTicketPO.getProcessType(), productPalletPO.getPalletSource());

        // 配置了上栈板数量，则不走自动换栈板操作，默认全部都是 无旧的上栈板
        if (ObjectUtil.isNotNull(ProductMaterialCount) && ObjectUtil.isNotNull(ProductMaterialCount.getPalletCount())
            && ProductMaterialCount.getPalletCount() > 0) {
            lastPalletOutVO.setHasOldPallet(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            return;
        }

        // 这个栈板码的生产工序
        String palletSource = productPalletPO.getPalletSource();

        // 获得当前机台的栈板中是否存在 上面这个栈板码 对应的工序的栈板
        LambdaQueryWrapper<ProductPalletPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductPalletPO::getRequisitionMachineName, machineName)
            .eq(ProductPalletPO::getStatus, PalletOperateStatusEnum.IN_PRODUCTION.getCode())
            .eq(ProductPalletPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()))
            .eq(ProductPalletPO::getPalletSource, palletSource);
        ProductPalletPO productPalletRequisitionPO = getOne(queryWrapper);
        if (ObjectUtil.isNull(productPalletRequisitionPO)) {
            lastPalletOutVO.setHasOldPallet(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        } else {
            // 上一个栈板的信息
            GetLastPalletByPalletCodeOutVO.LastPalletOutVO lastPalletOutVO1 =
                new GetLastPalletByPalletCodeOutVO.LastPalletOutVO();
            lastPalletOutVO1.setRemainingQuantity(BigDecimal.ZERO);
            lastPalletOutVO1.setConsumptionQuantity(productPalletRequisitionPO.getRemainingQuantity());
            lastPalletOutVO.setLastPalletOutVO(lastPalletOutVO1);
            lastPalletOutVO.setHasOldPallet(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }

    }

    @Override
    public List<LastPalletOutVO> getLastPalletList(Long productTicket) {
        LambdaQueryWrapper<ProductPalletPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductPalletPO::getRequisitionTicketId, productTicket)
            // 正在使用中的
            .eq(ProductPalletPO::getStatus, Integer.valueOf(PalletOperateStatusEnum.IN_PRODUCTION.getCode()));
        List<ProductPalletPO> productLastPalletPOS = list(queryWrapper);
        return BeanUtil.copyToList(productLastPalletPOS, LastPalletOutVO.class);
    }

    @Override
    public List<ProductPalletPO> getLastPalletList(List<Long> productTicketIdList,Integer status) {
        LambdaQueryWrapper<ProductPalletPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(ProductPalletPO::getProductTicketId, productTicketIdList)
                .eq(Objects.nonNull(status),ProductPalletPO::getStatus, status);
        return list(queryWrapper);
    }

    @Override
    public List<GetListByPalletSourceOutVO> getListByPalletSource(GetListByPalletSourceInVO inVO) {
        List<Long> productTicketIdList = Collections.singletonList(inVO.getProductTicket());
        LambdaQueryWrapper<ProductPalletPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtil.isNotEmpty(productTicketIdList),ProductPalletPO::getProductTicketId,productTicketIdList)
                .eq(StrUtil.isNotBlank(inVO.getPalletSource()),ProductPalletPO::getPalletSource,inVO.getPalletSource())
                .like(StrUtil.isNotBlank(inVO.getMachineName()),ProductPalletPO::getMachineName,inVO.getMachineName());
        queryWrapper.orderByDesc(ProductPalletPO::getCreateTime);
        List<ProductPalletPO> productLastPalletPOS = list(queryWrapper);
        return BeanUtil.copyToList(productLastPalletPOS, GetListByPalletSourceOutVO.class);
    }

    @Override
    public Pagination<LastPalletRecordsOutVO> getLastPalletRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<LastPalletRecordsOutVO> iPage = productPalletOperationRecordsService.getLastPalletRecords(productTicketPageInVO.getPage(),
            productTicketPageInVO.getProductTicketId());

        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        // 封装字段
        iPage.getRecords().stream().forEach(lastPalletRecordsOutVO -> {
            lastPalletRecordsOutVO.setStatusName(PalletOperateStatusEnum.getPalletOperateStatusEnum(lastPalletRecordsOutVO.getStatus()).getName());
        });
        return getLastPalletOutVOPagination(iPage, iPage);
    }

    @Override
    public Pagination<LastPalletRecordsOutVO> getLastPalletRecords(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        Page<LastPalletRecordsOutVO> iPage = productPalletOperationRecordsService.getLastPalletRecordsByTicketBase(page, productTicketBaseDTO);
        return getLastPalletOutVOPagination(page, iPage);
    }

    private Pagination<LastPalletRecordsOutVO> getLastPalletOutVOPagination(IPage page, Page<LastPalletRecordsOutVO> iPage) {
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds = Lists.newArrayList();
        Map<Long, String> userMap = Maps.newConcurrentMap();
        userIds.addAll(
            iPage.getRecords().stream().map(LastPalletRecordsOutVO::getCreateBy).distinct().collect(Collectors.toList()));
        String userIdsStr = iPage.getRecords().stream().map(p -> p.getTeamUsers()).filter(StringUtil::isNotBlank)
            .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(userIdsStr)) {
            userIds.addAll(Arrays.stream(userIdsStr.split(",")).filter(userId->{
                return !chinesePattern.matcher(userId).find();
            }).map(u -> Long.valueOf(u)).collect(Collectors.toList()));
            userMap.putAll(ia01Service.getUserInfoByIds(userIds).stream()
                .collect(Collectors.toMap(UserDTO::getId, UserDTO::getName)));
        }

        return Pagination.newInstance(iPage.getRecords().stream().map(lastPalletOutVO -> {
            lastPalletOutVO.setCreaterName(userMap.get(lastPalletOutVO.getCreateBy()));
            if (StringUtils.isBlank(lastPalletOutVO.getTeamUsers())) {
                return lastPalletOutVO;
            }
            if (!chinesePattern.matcher(lastPalletOutVO.getTeamUsers()).find()) {
                String userName = Arrays.stream(lastPalletOutVO.getTeamUsers().split(",")).filter(StringUtil::isNotBlank)
                        .map(id -> userMap.get(Long.valueOf(id))).collect(Collectors.joining(","));
                lastPalletOutVO.setTeamUsersName(userName);
            } else {
                lastPalletOutVO.setTeamUsersName(lastPalletOutVO.getTeamUsers());
            }

            return lastPalletOutVO;
        }).collect(Collectors.toList()), page);
    }

    @Override
    public Pagination<ProductPalletPO> getListByProductTickIds(Long pageNum, Long pageSize, List<Long> productTickIdList,String processName) {
        LambdaQueryWrapper<ProductPalletPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(processName),ProductPalletPO::getPalletSource,processName)
                .in(CollectionUtil.isNotEmpty(productTickIdList),ProductPalletPO::getProductTicketId,productTickIdList)
                .orderByDesc(ProductPalletPO::getCreateTime);
        IPage<ProductPalletPO> ipage = PageConvertor.toPage(pageNum,pageSize);
        IPage page = page(ipage, wrapper);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null,0,0);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }
}
