package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.channel.api.model.vo.out.SyncEventActiveOutVO;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.common.util.OssUtils;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.holder.UserContextHolder;
import cn.jihong.mes.production.api.model.dto.MessageStructDTO;
import cn.jihong.mes.production.api.model.dto.ProductBoxBarcodeDetailDetailDTO;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.vo.in.ExportBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeDetailDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.ExportBoxBarcodeOutVO;
import cn.jihong.mes.production.api.service.IProductBoxBarcodeDetailDetailService;
import cn.jihong.mes.production.api.service.IProductMqMessageService;
import cn.jihong.mes.production.api.service.IProductOutboundService;
import cn.jihong.mes.production.app.config.AliyunOssProperty;
import cn.jihong.mes.production.app.mapper.ProductBoxBarcodeDetailDetailMapper;
import cn.jihong.message.api.model.enums.MessageTypeEnum;
import cn.jihong.message.api.service.IEnterpriseWeChatService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.dto.MaterialsInfoDTO;
import cn.jihong.oa.erp.api.service.IMaterialsInfoService;
import cn.jihong.oa.erp.api.service.ISfaaTService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 箱码明细 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Slf4j
@DubboService
public class ProductBoxBarcodeDetailDetailServiceImpl
    extends JiHongServiceImpl<ProductBoxBarcodeDetailDetailMapper, ProductBoxBarcodeDetailDetailPO>
    implements IProductBoxBarcodeDetailDetailService {

    @Autowired
    private IProductOutboundService productOutboundService;
    @Autowired
    private IEnterpriseWeChatService enterpriseWeChatService;
    @Autowired
    private IProductMqMessageService productMqMessageService;
    @Resource
    private AliyunOssProperty aliyunOssProperty;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;
    @DubboReference
    private ISfaaTService sfaaTService;

    @Override
    public List<ProductBoxBarcodeDetailDetailPO> getByBarcodeNos(List<String> barcodeNos) {
        LambdaQueryWrapper<ProductBoxBarcodeDetailDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, barcodeNos)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite());
        return list(lambdaQueryWrapper);
    }

    @Override
    public void
        saveProductBoxBarcodeDetailDetailPOs(List<ProductBoxBarcodeDetailDetailDTO> productBoxBarcodeDetailDetailDTOs) {
        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS =
            productBoxBarcodeDetailDetailDTOs
                .stream().map(productBoxBarcodeDetailDetailDTO ->
                    {
                        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = BeanUtil
                                .copyProperties(productBoxBarcodeDetailDetailDTO, ProductBoxBarcodeDetailDetailPO.class);
                        productBoxBarcodeDetailDetailPO.setCreateBy(SecurityUtil.getUserId());
                        return productBoxBarcodeDetailDetailPO;
                    }
            )
                .collect(Collectors.toList());
        saveBatch(productBoxBarcodeDetailDetailPOS);
    }

    @Override
    public void saveProductBoxBarcodeDetailDetailPO(ProductBoxBarcodeDetailDetailDTO productBoxBarcodeDetailDetailDTO) {
        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO =
            BeanUtil.copyProperties(productBoxBarcodeDetailDetailDTO, ProductBoxBarcodeDetailDetailPO.class);
        save(productBoxBarcodeDetailDetailPO);
    }

    @Override
    public void activa(List<String> barcodeNos) {
        LambdaUpdateWrapper<ProductBoxBarcodeDetailDetailPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.in(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, barcodeNos)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .set(ProductBoxBarcodeDetailDetailPO::getActiva, Integer.valueOf(BooleanEnum.TRUE.getCode()));
        update(lambdaUpdateWrapper);
    }

    @Override
    public void activa(String barcodeNo) {
        LambdaUpdateWrapper<ProductBoxBarcodeDetailDetailPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, barcodeNo)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductBoxBarcodeDetailDetailPO::getActiva, Integer.valueOf(BooleanEnum.FALSE.getCode()))
                .set(ProductBoxBarcodeDetailDetailPO::getActiva, Integer.valueOf(BooleanEnum.TRUE.getCode()))
                .set(ProductBoxBarcodeDetailDetailPO::getUpdateBy, SecurityUtil.getUserId())
                .set(ProductBoxBarcodeDetailDetailPO::getUpdateTime, new java.util.Date());
        update(lambdaUpdateWrapper);
    }

    @Override
    public void updateSkuCode(String barcodeNo) {
        // 查找未激活的箱码
        LambdaQueryWrapper<ProductBoxBarcodeDetailDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, barcodeNo)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductBoxBarcodeDetailDetailPO::getActiva, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = getOne(lambdaQueryWrapper);
        if (productBoxBarcodeDetailDetailPO == null) {
            log.error("修改skucode失败，原因：条码{}不存在或已激活", barcodeNo);
            return;
        }
        // 更新激活的skucode
        MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(productBoxBarcodeDetailDetailPO.getMaterialCode());
        if (materialsInfoDTO != null
                && !StringUtils.equals(materialsInfoDTO.getImaaua017(), productBoxBarcodeDetailDetailPO.getSkuCode())) {
            log.info("条码skucode{}与erp中skucode{}不一致，更新为erp中的skucode", productBoxBarcodeDetailDetailPO.getSkuCode(),
                    materialsInfoDTO.getImaaua017());
            productBoxBarcodeDetailDetailPO.setSkuCode(materialsInfoDTO.getImaaua017());
            productBoxBarcodeDetailDetailPO.setUpdateBy(SecurityUtil.getUserId());
            productBoxBarcodeDetailDetailPO.setUpdateTime(new java.util.Date());

            updateById(productBoxBarcodeDetailDetailPO);
        }

    }

    @Override
    public void destroyBox(String barcodeNo) {
        LambdaUpdateWrapper<ProductBoxBarcodeDetailDetailPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, barcodeNo)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductBoxBarcodeDetailDetailPO::getActiva, Integer.valueOf(BooleanEnum.TRUE.getCode()))
                .set(ProductBoxBarcodeDetailDetailPO::getActiva, 2) // 2表示已销毁
                .set(ProductBoxBarcodeDetailDetailPO::getUpdateBy, SecurityUtil.getUserId())
                .set(ProductBoxBarcodeDetailDetailPO::getUpdateTime, new java.util.Date());
        update(lambdaUpdateWrapper);
    }

    @Override
    public void activeMessage(MessageStructDTO messageStructDTO) {
        String data = (String) messageStructDTO.getData();
        String[] barcodes = data.split(",");
        List<String> barcodeNoActiveList = Lists.newArrayList();
        List<String> barcodeActiveList = Lists.newArrayList();

        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = getByBarcodeNos(Arrays.asList(barcodes));
        Map<String, Integer> barcodeNoActivaMap = productBoxBarcodeDetailDetailPOS.stream().collect(Collectors
            .toMap(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, ProductBoxBarcodeDetailDetailPO::getActiva));

        Arrays.stream(barcodes).forEach(barcode -> {
            if (barcodeNoActivaMap.containsKey(barcode) && barcodeNoActivaMap.get(barcode).equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
                log.info("箱码: " + barcode + " 已激活,不需要重复激活");
                return;
            }
            log.info("激活箱码: " + barcode);
            UserInfo userInfo = messageStructDTO.getUserInfo();
            MockHttpServletRequest request = new MockHttpServletRequest();
            UserContextHolder.setUserInfo(userInfo);
            request.setAttribute("userContext", userInfo);
            RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
            // 更新skucode
            updateSkuCode(barcode);
            SyncEventActiveOutVO syncEventUploadOutVO = (SyncEventActiveOutVO)productOutboundService.activeBoxCode(barcode);

            if (!"true".equals(syncEventUploadOutVO.getSuccess())) {
                log.error("激活失败，原因：" + syncEventUploadOutVO.getErrorMsg());
                barcodeNoActiveList.add(barcode + ":激活失败，原因：" + syncEventUploadOutVO.getErrorMsg());
                return;
            }

            log.info("---激活箱码返回结果:syncEventUploadOutVO={}", com.alibaba.fastjson.JSON.toJSONString(syncEventUploadOutVO));
            // 提取msg字段的内容
            SyncEventActiveOutVO.DataVO dataVO = syncEventUploadOutVO.getData();
            int successCount = dataVO.getSuccessEpcSet().size();
            if (successCount > 0) {
                log.info("激活成功: " + barcode);
                barcodeActiveList.add(barcode);
                log.info("更新本地箱码激活状态: " + barcode);
                activa(barcode);
            } else {
                log.info("激活失败: " + barcode);
                barcodeNoActiveList.add(barcode);
            }

        });

        if (CollectionUtils.isNotEmpty(barcodeNoActiveList)) {
            enterpriseWeChatService.sendComTextMessageByParentCode(MessageTypeEnum.BOX_BAECODE_ACTIVE.getCode(),"激活失败:" + JSON.toJSONString(barcodeNoActiveList));
        }

        Map<String, List<String>> map = Maps.newHashMap();
        map.put("active", barcodeActiveList);
        map.put("noActive", barcodeNoActiveList);
        String message = JSON.toJSONString(map);
        // 记录到MQ发送历史消息中
        log.info("保存MQ消息: " + message);
        productMqMessageService.saveMqMessage(messageStructDTO, message);
    }

    @Override
    public List<ProductBoxBarcodeDetailDetailPO> getByDetailId(Long id) {
        LambdaQueryWrapper<ProductBoxBarcodeDetailDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductBoxBarcodeDetailDetailPO::getProductBarcodeDetailId, id)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite());
        return list(lambdaQueryWrapper);
    }

    @Override
    public Pagination<ProductBoxBarcodeDetailDetailDTO>
        getList(GetProductBoxBarcodeDetailDetailInVO getProductBoxBarcodeDetailDetailInVO) {
        LambdaQueryWrapper<ProductBoxBarcodeDetailDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtils.isNotBlank(getProductBoxBarcodeDetailDetailInVO.getBarcodeNo()), ProductBoxBarcodeDetailDetailPO::getBarcodeNo, getProductBoxBarcodeDetailDetailInVO.getBarcodeNo())
                .eq(StringUtils.isNotBlank(getProductBoxBarcodeDetailDetailInVO.getPlanTicketNo()), ProductBoxBarcodeDetailDetailPO::getPlanTicketNo, getProductBoxBarcodeDetailDetailInVO.getPlanTicketNo())
                .like(StringUtils.isNotBlank(getProductBoxBarcodeDetailDetailInVO.getProductName()), ProductBoxBarcodeDetailDetailPO::getProductName, getProductBoxBarcodeDetailDetailInVO.getProductName())
                .eq(StringUtils.isNotBlank(getProductBoxBarcodeDetailDetailInVO.getMaterialCode()), ProductBoxBarcodeDetailDetailPO::getMaterialCode, getProductBoxBarcodeDetailDetailInVO.getMaterialCode())
                .eq(ObjectUtils.isNotEmpty(getProductBoxBarcodeDetailDetailInVO.getProductionDate()),ProductBoxBarcodeDetailDetailPO::getProductionDate, getProductBoxBarcodeDetailDetailInVO.getProductionDate())
                .eq(StringUtils.isNotBlank(getProductBoxBarcodeDetailDetailInVO.getLotNo()), ProductBoxBarcodeDetailDetailPO::getLotNo, getProductBoxBarcodeDetailDetailInVO.getLotNo())
                .eq(StringUtils.isNoneBlank(getProductBoxBarcodeDetailDetailInVO.getShift()), ProductBoxBarcodeDetailDetailPO::getShift, getProductBoxBarcodeDetailDetailInVO.getShift())
                .eq(ObjectUtils.isNotEmpty(getProductBoxBarcodeDetailDetailInVO.getPrintStatus()), ProductBoxBarcodeDetailDetailPO::getPrintStatus, getProductBoxBarcodeDetailDetailInVO.getPrintStatus())
                .eq(ObjectUtils.isNotEmpty(getProductBoxBarcodeDetailDetailInVO.getActiva()), ProductBoxBarcodeDetailDetailPO::getActiva, getProductBoxBarcodeDetailDetailInVO.getActiva());
        IPage page = page(getProductBoxBarcodeDetailDetailInVO.getPage(), lambdaQueryWrapper);
        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = page.getRecords();
        if (CollectionUtils.isEmpty(productBoxBarcodeDetailDetailPOS)) {
            Pagination.newInstance(null);
        }
        return Pagination.newInstance(BeanUtil.copyToList(productBoxBarcodeDetailDetailPOS, ProductBoxBarcodeDetailDetailDTO.class),page);
    }

    @Override
    public ProductBoxBarcodeDetailDetailPO getByBarcodeNo(String barcode) {
        LambdaQueryWrapper<ProductBoxBarcodeDetailDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailDetailPO::getBarcodeNo, barcode)
                .eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite());
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public String exportBoxBarcode(ExportBoxBarcodeInVO exportBoxBarcodeInVO) {
        if (CollectionUtils.isEmpty(exportBoxBarcodeInVO.getIds())) {
            throw new CommonException("请选择需要导出的箱码段");
        }
        LambdaQueryWrapper<ProductBoxBarcodeDetailDetailPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductBoxBarcodeDetailDetailPO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductBoxBarcodeDetailDetailPO::getProductBarcodeDetailId, exportBoxBarcodeInVO.getIds());
        List<ProductBoxBarcodeDetailDetailPO> productBoxBarcodeDetailDetailPOS = list(lambdaQueryWrapper);

        List<ExportBoxBarcodeOutVO> productOrderWebVOS = Lists.newArrayList();
        for (int i = 0; i < productBoxBarcodeDetailDetailPOS.size(); i++) {
            ExportBoxBarcodeOutVO productOrderWebVO = new ExportBoxBarcodeOutVO();
            ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailPOS.get(i);
            String firstBarcode = productBoxBarcodeDetailDetailPO.getBarcodeNo().substring(0, 1 * 4);
            String secondBarcode = productBoxBarcodeDetailDetailPO.getBarcodeNo().substring(1 * 4, 2 * 4);
            String thirdBarcode = productBoxBarcodeDetailDetailPO.getBarcodeNo().substring(2 * 4, 3 * 4);
            String fourthBarcode = productBoxBarcodeDetailDetailPO.getBarcodeNo().substring(3 * 4, 4 * 4);
            String fifthBarcode = productBoxBarcodeDetailDetailPO.getBarcodeNo().substring(4 * 4, 5 * 4);
            String sixthBarcode = productBoxBarcodeDetailDetailPO.getBarcodeNo().substring(5 * 4, 6 * 4);

            productOrderWebVO.setFirst(firstBarcode);
            productOrderWebVO.setSecond(secondBarcode);
            productOrderWebVO.setThird(thirdBarcode);
            productOrderWebVO.setFourth(fourthBarcode);
            productOrderWebVO.setFifth(fifthBarcode);
            productOrderWebVO.setSixth(sixthBarcode);

            productOrderWebVO.setSeventh(firstBarcode);
            productOrderWebVO.setEighth(secondBarcode);
            productOrderWebVO.setNinth(thirdBarcode);
            productOrderWebVO.setTenth(fourthBarcode);
            productOrderWebVO.setEleventh(fifthBarcode);
            productOrderWebVO.setTwelfth(sixthBarcode);
            productOrderWebVOS.add(productOrderWebVO);
        }

        try{
            // 导出文件名称
            StringBuilder fileNameStringBuilder = new StringBuilder();
            fileNameStringBuilder.append("OCOC码段-");
            fileNameStringBuilder.append(LocalDate.now().toString());

            // 写Excel
            ExcelWriter excelWriter = null;
            ByteArrayInputStream byteArrayInputStream = null;
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            try {
                excelWriter = EasyExcel.write(os).build();
                WriteSheet sheetProductOrder = EasyExcel.writerSheet(0,"条码").head(ExportBoxBarcodeOutVO.class).build();
                sheetProductOrder.setNeedHead(Boolean.FALSE);
                excelWriter.write(productOrderWebVOS,sheetProductOrder);
                excelWriter.finish();
                // 设置 oss 的路径信息
                String OBJECT_NAME = "temp";
                String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
                String objectName = OBJECT_NAME + "/" + dateStr + "/" + System.currentTimeMillis()
                        + "/" + fileNameStringBuilder.toString() + ".xlsx";
                byteArrayInputStream = new ByteArrayInputStream(os.toByteArray());
                String uploadFile = OssUtils.uploadFile(aliyunOssProperty.getAccessId(), aliyunOssProperty.getAccessKey(),
                        aliyunOssProperty.getHzEndpoint(), aliyunOssProperty.getCommissionBucketName(), objectName,
                        byteArrayInputStream);
                return uploadFile;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                byteArrayInputStream.close();
            }
        }catch (Exception e){
            log.error("error {}"+e.getStackTrace());
            log.error("errormessage {}"+e.getMessage());
        }
        return null;
    }


    public static String extractField(String json, String field) {
        String pattern = "\"" + field + "\":\\s*\"([^\"]+)\"";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(json);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }

    public static int extractCount(String text, String pattern) {
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(text);
        if (m.find()) {
            return Integer.parseInt(m.group(1));
        }
        return 0;
    }


}
