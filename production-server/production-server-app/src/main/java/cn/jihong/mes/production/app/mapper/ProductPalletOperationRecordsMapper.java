package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.dto.ProductPalletOperationRecordsDTO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductPalletOperationRecordsPO;
import cn.jihong.mes.production.api.model.vo.out.LastPalletRecordsOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

import java.util.List;

/**
 * <p>
 * 栈板操作记录信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
public interface ProductPalletOperationRecordsMapper extends JiHongMapper<ProductPalletOperationRecordsPO> {

    Page<LastPalletRecordsOutVO> getLastPalletRecordsByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);

    Page<LastPalletRecordsOutVO> getLastPalletRecords(IPage page, @Param("productTicketId") Long productTicketId, @Param("companyCode")String companyCode);

    List<ProductPalletOperationRecordsDTO> getByProductTicketIds(@Param("productTicketIds") List<Long> productTicketIds,
        @Param("operationTypes") List<Integer> operationTypes);

    Page<LastPalletRecordsOutVO> getDownPalletRecords(IPage page, @Param("productTicketId") Long productTicketId,@Param("companyCode") String companyCode);
}
