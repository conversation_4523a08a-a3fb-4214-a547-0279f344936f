package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ProductMachineTicketDTO;
import cn.jihong.mes.production.api.model.po.ProductMachineTicketPO;
import cn.jihong.mes.production.api.service.IProductMachineTicketService;
import cn.jihong.mes.production.app.mapper.ProductMachineTicketMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Date;

/**
 * <p>
 * 生产机台工程单绑定表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@DubboService
public class ProductMachineTicketServiceImpl extends
    JiHongServiceImpl<ProductMachineTicketMapper, ProductMachineTicketPO> implements IProductMachineTicketService {

    @Override
    public void save(ProductMachineTicketDTO productMachineTicketDTO) {
        ProductMachineTicketPO productMachineTicketPO = new ProductMachineTicketPO();
        BeanUtil.copyProperties(productMachineTicketDTO,productMachineTicketPO);
        Long userId = SecurityUtil.getUserId();
        productMachineTicketPO.setCreateTime(new Date());
        productMachineTicketPO.setCreateBy(userId==null?null:userId);
        save(productMachineTicketPO);
    }

    @Override
    public ProductMachineTicketPO getByMachineName(String machineName) {
        LambdaQueryWrapper<ProductMachineTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMachineTicketPO::getMachineName,machineName)
                .eq(ProductMachineTicketPO::getStatus,"1");
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public void updateByMachineName(String machineName, String planTicketNo) {
        LambdaUpdateWrapper<ProductMachineTicketPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ProductMachineTicketPO::getMachineName,machineName)
//                .set(ProductMachineTicketPO::getPlanTicketNo,null)
                .eq(StringUtils.isNotBlank(planTicketNo),ProductMachineTicketPO::getPlanTicketNo,planTicketNo)
                .eq(ProductMachineTicketPO::getStatus,"1")
                .set(ProductMachineTicketPO::getUpdateBy, SecurityUtil.getUserId())
                .set(ProductMachineTicketPO::getStatus, "0")
        ;
        update(lambdaUpdateWrapper);
    }
}
