package cn.jihong.mes.production.app.enums;

import cn.jihong.calibre.review.server.api.model.enums.InspectionPeopleJobEnum;
import cn.jihong.mes.production.api.model.enums.*;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum EnumTypeMapping {
    FIRST_CHECK_STATUS_ENUM("FirstCheckStatusEnum", FirstCheckStatusEnum.class),
    MACHINE_TASK_TYPE_ENUM("MachineTaskTypeEnum", MachineTaskTypeEnum.class),
    MATERIAL_ENUM("MaterialEnum", MaterialEnum.class),
    MATERIAL_OPERATE_TYPE_ENUM("MaterialOperateTypeEnum", MaterialOperateTypeEnum.class),
    MQ_BIZ_CODE_ENUM("MQBizCodeEnum", MQBizCodeEnum.class),
    OUT_BOUND_ENUM("OutboundEnum", OutboundEnum.class),
    PALLET_OPERATE_STATUS_ENUM("PalletOperateStatusEnum", PalletOperateStatusEnum.class),
    PALLET_OPERATE_TYPE_ENUM("PalletOperateTypeEnum", PalletOperateTypeEnum.class),
    PRODUCT_SHIT_ENUM("ProductShitEnum", ProductShitEnum.class),
    SHIFTNO_ENUM("ShiftNoEnum", ShiftNoEnum.class),
    UNIT_ENUM("UnitEnum", UnitEnum.class),
    INSPECTION_PEOPLE_JOB_ENUM("InspectionPeopleJobEnum", InspectionPeopleJobEnum.class),
    COMPANY_BOX_ENUM("CompanyBoxEnum", CompanyBoxEnum.class),
    FLIGHT_ENUM("MachinePartsEnum", MachinePartsEnum.class),
    STORE_STATUS_ENUM("StoreStatusEnum", StoreStatusEnum.class),
    WORK_SHOP_ENUM("WorkShopEnum", WorkShopEnum.class),
    MESH_TYPE_ENUM("MeshTypeEnum", MeshTypeEnum.class),
    ;

    private final String enumTypeName;
    private final Class clz;

    EnumTypeMapping(String enumTypeName, Class clz) {
        this.enumTypeName = enumTypeName;
        this.clz = clz;
    }



    public static Map<String, Class> getEnumTypeMap() {
        Map<String, Class> enumTypeMap = new HashMap<>();
        for (EnumTypeMapping mapping : values()) {
            enumTypeMap.put(mapping.getEnumTypeName(), mapping.getClz());
        }
        return enumTypeMap;
    }
}
