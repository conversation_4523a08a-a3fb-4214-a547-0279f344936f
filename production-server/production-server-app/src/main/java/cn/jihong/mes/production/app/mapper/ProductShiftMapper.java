package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductShiftPO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 班次信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
public interface ProductShiftMapper extends JiHongMapper<ProductShiftPO> {

    Page<ProductShiftOutVO> getProductShift(IPage page, @Param("productTicketId") Long productTicketId, @Param("companyCode") String companyCode);

    Page<ProductShiftOutVO> getProductShiftByTicketBase(IPage page,
        @Param("productTicketBaseDTO") ProductTicketBaseDTO productTicketBaseDTO);


}
