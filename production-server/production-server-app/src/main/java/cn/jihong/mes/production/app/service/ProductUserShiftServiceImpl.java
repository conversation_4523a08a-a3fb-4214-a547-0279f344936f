package cn.jihong.mes.production.app.service;

import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductUserShiftPO;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mes.production.api.service.IProductUserShiftService;
import cn.jihong.mes.production.app.mapper.ProductUserShiftMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 员工班组信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@DubboService
public class ProductUserShiftServiceImpl extends JiHongServiceImpl<ProductUserShiftMapper, ProductUserShiftPO> implements IProductUserShiftService {

    @DubboReference
    private IA01Service ia01Service;


    @Override
    public Long saveProductShift(ProductShiftInVO productShiftInVO) {
        List<ProductUserShiftPO> productShiftPOS = productShiftInVO.getTeamUsers().stream().distinct().map(
                productShift -> {
            ProductUserShiftPO productShiftPO = new ProductUserShiftPO();
            productShiftPO.setCompanyCode(SecurityUtil.getCompanySite());
            productShiftPO.setUserId(SecurityUtil.getUserId());
            productShiftPO.setRoleCode(productShift.getRoleCode());
            productShiftPO.setRoleName(productShift.getRoleName());
            productShiftPO.setTeamUsers(productShift.getUserId());
            productShiftPO.setCreateBy(SecurityUtil.getUserId());
            return productShiftPO;
        }).collect(Collectors.toList());
        saveBatch(productShiftPOS);
        return 1L;
    }

    @Override
    public ProductShiftInVO getProductShift() {
        ProductShiftInVO productShiftInVO = new ProductShiftInVO();
        LambdaQueryWrapper<ProductUserShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductUserShiftPO::getCompanyCode,SecurityUtil.getCompanySite())
                .eq(ProductUserShiftPO::getUserId,SecurityUtil.getUserId());
        List<ProductUserShiftPO> productShiftPOS = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(productShiftPOS)) {
            List<ProductShiftInVO.TeamUser> teamUsers = productShiftPOS.stream().map(productShiftPO -> {
                ProductShiftInVO.TeamUser teamUser = new ProductShiftInVO.TeamUser();
                teamUser.setUserId(productShiftPO.getTeamUsers());
                teamUser.setRoleCode(productShiftPO.getRoleCode());
                teamUser.setRoleName(productShiftPO.getRoleName());
                return teamUser;
            }).collect(Collectors.toList());
            productShiftInVO.setTeamUsers(teamUsers);

            // 赋值名称
            List<Long> userIds = teamUsers.stream().map(teamUser -> Long.valueOf(teamUser.getUserId())).collect(Collectors.toList());
            Map<Long, String> userMap =
                    ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
            teamUsers.forEach(teamUser -> {
                teamUser.setUserName(userMap.get(Long.valueOf(teamUser.getUserId())));
            });

        }
        return productShiftInVO;
    }

    @Override
    public void updateProductShift(ProductShiftInVO productShiftInVO) {
        // 根据用户id,去更新，新增，删除

        // 1. 查询数据库中当前用户的现有班次记录
        LambdaQueryWrapper<ProductUserShiftPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ProductUserShiftPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductUserShiftPO::getCreateBy, SecurityUtil.getUserId());
        List<ProductUserShiftPO> existingRecords = list(queryWrapper);

        // 2. 将现有记录转换为Map，以userId+roleCode作为唯一标识
        Map<String, ProductUserShiftPO> existingMap = existingRecords.stream()
                .collect(Collectors.toMap(
                    record -> record.getTeamUsers() + "_" + record.getRoleCode(),
                    record -> record
                ));

        // 3. 处理前端传来的数据
        for (ProductShiftInVO.TeamUser teamUser : productShiftInVO.getTeamUsers()) {
            String key = teamUser.getUserId() + "_" + teamUser.getRoleCode();
            ProductUserShiftPO existingRecord = existingMap.get(key);

            if (existingRecord == null) {
                // 新增记录
                ProductUserShiftPO newRecord = new ProductUserShiftPO();
                newRecord.setCompanyCode(SecurityUtil.getCompanySite());
                newRecord.setUserId(SecurityUtil.getUserId());
                newRecord.setRoleCode(teamUser.getRoleCode());
                newRecord.setRoleName(teamUser.getRoleName());
                newRecord.setTeamUsers(teamUser.getUserId());
                newRecord.setCreateBy(SecurityUtil.getUserId());
                save(newRecord);
            } else {
                // 检查是否需要更新
                boolean needUpdate = false;
                if (!teamUser.getRoleName().equals(existingRecord.getRoleName())) {
                    existingRecord.setRoleName(teamUser.getRoleName());
                    needUpdate = true;
                }

                if (needUpdate) {
                    existingRecord.setUpdateBy(SecurityUtil.getUserId());
                    updateById(existingRecord);
                }
            }
        }

        // 4. 删除数据库中存在但前端未传递的记录
        Set<String> frontendKeys = productShiftInVO.getTeamUsers().stream()
                .map(teamUser -> teamUser.getUserId() + "_" + teamUser.getRoleCode())
                .collect(Collectors.toSet());

        for (ProductUserShiftPO existingRecord : existingRecords) {
            String key = existingRecord.getTeamUsers() + "_" + existingRecord.getRoleCode();
            if (!frontendKeys.contains(key)) {
                removeById(existingRecord.getId());
            }
        }
    }
}
