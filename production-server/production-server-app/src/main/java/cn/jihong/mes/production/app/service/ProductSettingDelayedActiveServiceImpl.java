package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.production.api.model.dto.ProductSettingDelayedActiveDTO;
import cn.jihong.mes.production.api.model.po.ProductSettingDelayedActivePO;
import cn.jihong.mes.production.api.service.IProductSettingDelayedActiveService;
import cn.jihong.mes.production.app.mapper.ProductSettingDelayedActiveMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * 工单配置箱码延迟激活 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@DubboService
public class ProductSettingDelayedActiveServiceImpl
    extends JiHongServiceImpl<ProductSettingDelayedActiveMapper, ProductSettingDelayedActivePO>
    implements IProductSettingDelayedActiveService {

    @Override
    public Pagination<ProductSettingDelayedActiveDTO> getPage(PageRequest pageRequest) {
        LambdaQueryWrapper<ProductSettingDelayedActivePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductSettingDelayedActivePO::getCompanyCode, SecurityUtil.getCompanySite())
            .orderByDesc(ProductSettingDelayedActivePO::getCreateTime);

        IPage page = page(pageRequest.getPage(), lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<ProductSettingDelayedActiveDTO> productSettingDelayedActiveDTOS =
                BeanUtil.copyToList(page.getRecords(), ProductSettingDelayedActiveDTO.class);
            return Pagination.newInstance(productSettingDelayedActiveDTOS, page);
        }
        return Pagination.newInstance(null);

    }

    @Override
    public Long add(ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO) {
        ProductSettingDelayedActivePO productSettingDelayedActivePO = BeanUtil.copyProperties(productSettingDelayedActiveDTO, ProductSettingDelayedActivePO.class);
        productSettingDelayedActivePO.setId(null);
        save(productSettingDelayedActivePO);
        return productSettingDelayedActivePO.getId();
    }

    @Override
    public Long update(ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO) {
        ProductSettingDelayedActivePO productSettingDelayedActivePO = BeanUtil.copyProperties(productSettingDelayedActiveDTO, ProductSettingDelayedActivePO.class);
        updateById(productSettingDelayedActivePO);
        return productSettingDelayedActivePO.getId();
    }

    @Override
    public List<ProductSettingDelayedActiveDTO> getList(ProductSettingDelayedActiveDTO productSettingDelayedActiveDTO) {
        LambdaQueryWrapper<ProductSettingDelayedActivePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductSettingDelayedActivePO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(StringUtil.isNotEmpty(productSettingDelayedActiveDTO.getMaterialCode()), ProductSettingDelayedActivePO::getMaterialCode, productSettingDelayedActiveDTO.getMaterialCode())
                .orderByDesc(ProductSettingDelayedActivePO::getCreateTime);

        List<ProductSettingDelayedActivePO> list = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, ProductSettingDelayedActiveDTO.class);
        }
        return null;
    }

}
