package cn.jihong.mes.production.app;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * 后台管理启动类
 *
 * <AUTHOR>
 * Created on 2019/9/18
 */
@SpringBootApplication
@MapperScan("cn.jihong.mes.production.app.mapper")
@EnableDubbo(scanBasePackages = "cn.jihong.mes.production.app.service")
@EnableScheduling
@EnableAspectJAutoProxy
public class ProductionBootstrap extends SpringBootServletInitializer {

	public static void main(String[] args) {
		SpringApplication.run(ProductionBootstrap.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(ProductionBootstrap.class);
	}

}
