package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.MeshDTO;
import cn.jihong.mes.production.api.model.po.MeshPO;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetMeshListInVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.MeshByTypeAndMeshIdInVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.GetMeshListOutVO;
import cn.jihong.mes.production.api.service.IMeshService;
import cn.jihong.mes.production.app.mapper.MeshMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-24 14:54
 */
@Slf4j
@DubboService
public class MeshServiceImpl extends JiHongServiceImpl<MeshMapper, MeshPO> implements IMeshService {

    @Override
    public List<MeshPO> getMeshByMeshType(Integer type) {
        LambdaQueryWrapper<MeshPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MeshPO::getMeshType,type);
        return list(wrapper);
    }

    @Override
    public List<GetMeshListOutVO> getMeshList(GetMeshListInVO inVO) {
        LambdaQueryWrapper<MeshPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MeshPO::getWorkshopCode,inVO.getWorkshopCode())
                .eq(MeshPO::getMeshType,inVO.getMeshType());
        return BeanUtil.copyToList(list(wrapper), GetMeshListOutVO.class);
    }

    @Override
    public MeshDTO getMeshByMeshId(MeshByTypeAndMeshIdInVO meshByTypeAndMeshIdInVO) {
        LambdaQueryWrapper<MeshPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MeshPO::getMeshId,meshByTypeAndMeshIdInVO.getMeshId())
                .eq(MeshPO::getWorkshopCode,meshByTypeAndMeshIdInVO.getWorkshopCode())
                .in(CollectionUtil.isNotEmpty(meshByTypeAndMeshIdInVO.getMeshTypes()),
                        MeshPO::getMeshType,meshByTypeAndMeshIdInVO.getMeshTypes());
        List<MeshPO> list = list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return BeanUtil.copyProperties(list.get(0), MeshDTO.class) ;
    }

    @Override
    public void updateMeshByMsehId(Integer code, List<String> meshs, String workshopCode) {
        if (CollectionUtil.isEmpty(meshs)) {
            log.info("网带编号为空，不进行更新");
            return;
        }
        // 新增 修改
        meshs.forEach(meshId -> {
            LambdaQueryWrapper<MeshPO> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(MeshPO::getMeshId, meshId)
                    .eq(MeshPO::getMeshType,code);
            List<MeshPO> list = list(wrapper);
            if (CollectionUtil.isEmpty(list)) {
                MeshPO meshPO = new MeshPO();
                meshPO.setCompanyCode(SecurityUtil.getCompanySite());
                meshPO.setMeshId(meshId);
                meshPO.setMeshType(code);
                meshPO.setWorkshopCode(workshopCode);
                meshPO.setCreateBy(SecurityUtil.getUserId());
                meshPO.setUpdateBy(SecurityUtil.getUserId());
                meshPO.setCreateTime(new Date());
                meshPO.setUpdateTime(new Date());
                save(meshPO);
            }
        });
    }
}
