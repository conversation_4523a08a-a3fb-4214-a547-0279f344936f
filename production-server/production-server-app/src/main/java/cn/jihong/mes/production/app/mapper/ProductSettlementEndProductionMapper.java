package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionListOutVO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.jihong.mes.production.api.model.po.ProductSettlementEndProductionPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementEndProductionCollectDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionCollectDetailOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;

import java.util.List;

/**
 * <p>
 * 工程结算产成品信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface ProductSettlementEndProductionMapper extends JiHongMapper<ProductSettlementEndProductionPO> {


    List<GetSettlementEndProductionListOutVO> getSettlementEndProductionList(@Param("productTicketNo") String productTicketNo);


    Page<GetSettlementEndProductionCollectDetailOutVO> getSettlementEndProductionCollectDetail(IPage page, @Param("inVo") GetSettlementEndProductionCollectDetailInVO inVo);

}
