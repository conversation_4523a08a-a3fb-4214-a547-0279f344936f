package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/19 17:31
 */
@Data
public class GetPageByEndDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = -2510345761369274075L;

    /**
     * 工厂代码不能为空
     */
    @NotBlank(message = "工厂代码不能为空")
    private String companyCode;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

    /**
     * 截止日期
     */
    private String endDate;

}
