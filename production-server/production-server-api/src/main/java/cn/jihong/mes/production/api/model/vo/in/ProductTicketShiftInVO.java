package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ProductTicketShiftInVO extends PageRequest {

    /**
     * 公司code
     */
    private String companyCode;
    /**
     * 机台名称
     */
    private String machineName;
    /**
     * 工程单号
     */
    private String planTicketNo;
    /**
     * 生产日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;
    /**
     * 生产班次
     */
    private Integer shift;

}
