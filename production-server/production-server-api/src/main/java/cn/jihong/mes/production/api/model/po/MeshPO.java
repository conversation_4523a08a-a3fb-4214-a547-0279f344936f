package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 网带
 * <AUTHOR>
 * @date 2025-02-24 14:42
 */
@Getter
@Setter
@TableName("mesh")
public class MeshPO implements Serializable {


    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String WORKSHOP_CODE = "workshop_code";
    public static final String MESH_ID = "mesh_id";
    public static final String MESH_TYPE = "mesh_type";
    public static final String PALLET_CODE = "pallet_code";
    public static final String REMARK = "remark";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 工厂代码
     */
    @TableField(WORKSHOP_CODE)
    private String workshopCode;

    /**
     *  对应物流网带id
     */
    @TableField(MESH_ID)
    private String meshId;

    /**
     *  网带类型（1：供料  2：出口）
     */
    @TableField(MESH_TYPE)
    private int meshType;


    /**
     * 备注
     */
    @TableField(REMARK)
    private String remark;

    /**
     * 栈板码
     */
    @TableField(PALLET_CODE)
    private String palletCode;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
