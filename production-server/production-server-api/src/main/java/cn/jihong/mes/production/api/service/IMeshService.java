package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.dto.MeshDTO;
import cn.jihong.mes.production.api.model.po.MeshPO;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetMeshListInVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.MeshByTypeAndMeshIdInVO;
import cn.jihong.mes.production.api.model.vo.out.logistics.GetMeshListOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-24 14:48
 */
public interface IMeshService extends IJiHongService<MeshPO> {


    List<MeshPO> getMeshByMeshType(Integer type);

    List<GetMeshListOutVO> getMeshList(GetMeshListInVO inVO);

    MeshDTO getMeshByMeshId(MeshByTypeAndMeshIdInVO meshByTypeAndMeshIdInVO);

    void updateMeshByMsehId(Integer code, List<String> meshs, String workshopCode);
}
