package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PushWmsAndErpInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 栈板码
     */
    @NotBlank(message = "栈板码不能为空")
    private String palletCode;

    /**
     * 箱码
     */
    @NotBlank(message = "箱码不能为空")
    private String caseCode;

    /**
     * 已生产
     */
    @NotNull(message = "已生产数量不能为空")
    private BigDecimal producedQuantity;

    /**
     * 超产原因
     */
    private String reason;

    /**
     * 单位
     */
    // @NotBlank(message = "单位不能为空")
    private String unit;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;


}
