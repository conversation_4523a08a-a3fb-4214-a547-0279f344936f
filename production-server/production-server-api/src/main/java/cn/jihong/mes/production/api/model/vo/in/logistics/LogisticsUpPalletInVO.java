package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class LogisticsUpPalletInVO implements Serializable {

    private String palletCode;

    private BigDecimal palletQuantity;

    private String machineName;

    private Long planTickId;


    private String meshId;
    private String workshopCode;

    /**
     * 网带类型  1 供料  2 出口  3 备料  4 上料
     */
    private Integer meshType;

}
