package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-03-21 20:33
 */
@Data
public class GetTileWireProductPlanListInVO implements Serializable {


    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String erpMachineName;

    /**
     * 生产日期
     */
    @NotNull(message = "生产日期不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanDate;

    /**
     * 班次
     */
    @NotNull(message = "班次不能为空")
    private Integer serialNo;




}
