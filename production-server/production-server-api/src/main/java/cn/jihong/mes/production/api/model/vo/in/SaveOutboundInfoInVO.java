package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SaveOutboundInfoInVO extends ProductTicketVO implements Serializable {

    /**
     * 设备止码
     */
    private Integer machineStopNo;

    /**
     * 栈板码
     */
//    @NotBlank(message = "栈板码不能为空")
    private String palletCode;

    /**
     * 栈板短码
     */
    private String palletShortCode;

    /**
     * 箱码
     */
    private String caseCode;

    /**
     * 已生产
     */
    @NotNull(message = "已生产数量不能为空")
    private BigDecimal producedQuantity;

    /**
     * 超产原因
     */
    private String reason;

    /**
     * 单位
     */
//    @NotBlank(message = "单位不能为空")
    private String unit;

    /**
     * 栈板信息
     */
    private List<PalletCodeInfo> palletCodeInfos;

    /**
     * 单别
     */
    private String docType;

    /**
     * 是否是成品
     */
    private Integer isFinalProduct = 1;

    @Data
    public static class PalletCodeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 栈板数量
         */
        @NotNull(message = "栈板数量不能为空")
        private BigDecimal palletCodeQuantity;

        /**
         * 栈板码
         */
        private String palletCode;

    }


    /**
     * ids内部托盘id
     */
    private String idsPalletId;

    /**
     * ids 工单号
     */
    private String idsWorkOrderNo;

}
