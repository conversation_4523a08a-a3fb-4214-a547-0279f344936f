package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/19 17:31
 */
@Data
public class GetPageByEndOutVO implements Serializable {
    private static final long serialVersionUID = 731624855472079026L;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 截止日期
     */
    private String produceDate;

    /**
     * 多个工序的截止日期
     */
    private String produceDateConcat;

    /**
     * 工序code
     */
    private String processCode;

    /**
     * 多个工序的工序code
     */
    private String processCodeConcat;


    /**
     * 工序
     */
    private String process;

    /**
     * 多个工序的工序
     */
    private String processConcat;

    /**
     * 单位（报工）
     */
    private String reportedUnit;

    /**
     * 多个工序的单位（报工）
     */
    private String reportedUnitConcat;

    /**
     * 开单数量
     */
    private BigDecimal billingQuantity;

    /**
     * 开单数量（PCS）
     */
    private BigDecimal billingPcsQuantity;

    /**
     * 计划产量
     */
    private BigDecimal totalPlannedQuantity;

    /**
     * 多个工序的计划产量
     */
    private String totalPlannedQuantityConcat;

    /**
     * 计划产量（PCS）
     */
    private BigDecimal totalPlannedPcsQuantity;

    /**
     * 已生产数量
     */
    private BigDecimal totalReportedQuantity;

    /**
     * 多个工序的已生产数量
     */
    private String totalReportedQuantityConcat;

    /**
     * 已生产数量（PCS）
     */
    private BigDecimal totalReportedPcsQuantity;


    /**
     * 不良品数量
     */
    private BigDecimal totalDefectiveProductsQuantity;

    /**
     * 不良品数量（PCS）
     */
    private BigDecimal totalDefectiveProductsPcsQuantity;

    /**
     * 开单损耗率
     */
    private BigDecimal billingAttritionRate;

    /**
     * 实际损耗率
     */
    private BigDecimal attritionRate;

    /**
     * 超损数量
     */
    private BigDecimal excessPcsQuantity;
}
