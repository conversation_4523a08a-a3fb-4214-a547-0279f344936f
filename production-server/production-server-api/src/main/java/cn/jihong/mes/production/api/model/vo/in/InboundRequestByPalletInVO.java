package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class InboundRequestByPalletInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 栈板信息
     */
    @NotNull(message = "栈板码不能为空")
    @Size(min = 1, message = "栈板码最小为1")
    @Valid
    private List<PalletCodeInfo> palletCodeInfos;

    /**
     * 是否是成品
     */
    private Integer isFinalProduct = 1;


    @Data
    public static class PalletCodeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 栈板数量
         */
        @NotNull(message = "栈板数量不能为空")
        private BigDecimal palletCodeQuantity;

        /**
         * 栈板码
         */
        private String palletCode;

    }

    /**
     * 已生产
     */
    @NotNull(message = "已生产数量不能为空")
    private BigDecimal producedQuantity;

    /**
     * 超产原因
     */
    private String reason;

    /**
     * 单位
     */
    // @NotBlank(message = "单位不能为空")
    private String unit;

    /**
     * 工程单号
     */
    @NotBlank(message = "工程单号不能为空")
    private String planTicketNo;

}
