package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductShiftPO;
import cn.jihong.mes.production.api.model.vo.in.GetDefaultRoleListsInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.out.GetDefaultRoleListsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetRoleListsOutVO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 班次信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
public interface IProductShiftService extends IJiHongService<ProductShiftPO> {

    String saveProductShift(ProductShiftInVO productShiftInVO);

    Pagination<ProductShiftOutVO> getProductShift(ProductTicketPageInVO productTicketPageInVO);

    Pagination<ProductShiftOutVO> getProductShift(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    List<ProductShiftOutVO> getProductShift(List<Long> productTicketId);

    void updateProductShift(ProductShiftInVO productShiftInVO);

    /**
     * 获得角色、岗位列表
     * @return
     */
    List<GetRoleListsOutVO> getRoleLists();

    List<GetDefaultRoleListsOutVO> getDefaultRoleLists(GetDefaultRoleListsInVO getDefaultRoleListsInVO);

    List<ProductShiftInVO.TeamUser> getResponsibleShift(Long planTicketId);
}
