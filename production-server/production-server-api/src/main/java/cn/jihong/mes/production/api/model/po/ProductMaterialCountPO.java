package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 机台上料和上栈板的数量
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Getter
@Setter
@TableName("product_material_count")
public class ProductMaterialCountPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PROCESS_TYPE = "process_type";
    public static final String PROCESS = "process";
    public static final String MATERIAL_TYPE = "material_type";
    public static final String MATERIAL_PLACE = "material_place";
    public static final String PALLET_COUNT = "pallet_count";
    public static final String MATERIAL_COUNT = "material_count";
    public static final String REMARK = "remark";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * 主键id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 工序类型
     */
    @TableField(PROCESS_TYPE)
    private String processType;


    /**
     * 上一道工序
     */
    @TableField(PROCESS)
    private String process;


    /**
     * 物料类型
     */
    @TableField(MATERIAL_TYPE)
    private String materialType;

    /**
     * 物料类型
     */
    @TableField(MATERIAL_PLACE)
    private String materialPlace;


    /**
     * 栈板数量
     */
    @TableField(PALLET_COUNT)
    private Integer palletCount;


    /**
     * 物料数量
     */
    @TableField(MATERIAL_COUNT)
    private Integer materialCount;


    /**
     * 备注
     */
    @TableField(REMARK)
    private String remark;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 修改时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除：0-否，1-是
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
