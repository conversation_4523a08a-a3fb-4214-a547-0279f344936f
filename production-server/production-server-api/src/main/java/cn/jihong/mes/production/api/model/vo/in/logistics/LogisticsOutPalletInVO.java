package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class LogisticsOutPalletInVO implements Serializable {

    private String machineName;

    private Long planTickId;

    /**
     * 栈板码
     */
    @NotBlank(message = "栈板码不能为空")
    private String palletCode;

    /**
     * 已生产
     */
    @NotNull(message = "已生产数量不能为空")
    private BigDecimal producedQuantity;

    /**
     * 单位
     */
//    @NotBlank(message = "单位不能为空")
    private String unit;

    /**
     * ids 工单号
     */
    private String idsWorkOrderNo;



}
