package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.GetOeeDTO;
import cn.jihong.mes.production.api.model.po.EquipmentDowntimePO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeInVO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeListInVO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimePageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetOeeInVO;
import cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetOeeEquipmentDowntime;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;


/**
 * 设备停机代码表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface IEquipmentDowntimeService extends IJiHongService<EquipmentDowntimePO> {

        /**
         * 保存
         * @param inVO
         * @return: Boolean
         */
        Boolean saveEquipmentDowntime(EquipmentDowntimeInVO inVO);

        /**
         * 更新
         * @param inVO
         * @return: Boolean
         */
        Boolean editEquipmentDowntime(EquipmentDowntimeInVO inVO);

        /**
         * 批量删除
         * @param ids
         * @return: Boolean
         */
        Boolean deleteByIds(String ids);


        /**
         * 获取详情
         * @param id
         * @return: EquipmentDowntimeOutVO
         */
        EquipmentDowntimeOutVO getSingleEquipmentDowntimeById(Long id);
        /**
         * 获取详情
         * @param code
         * @return: EquipmentDowntimeOutVO
         */
        EquipmentDowntimeOutVO getSingleEquipmentDowntimeByCode(String code);

        /**
         * 获取分页列表
         * @param inVO
         * @return: Pagination<EquipmentDowntimeOutVO>
         */
        Pagination<EquipmentDowntimeOutVO> getEquipmentDowntimePage(EquipmentDowntimePageInVO inVO);

        List<EquipmentDowntimeOutVO> getEquipmentDowntimeList(EquipmentDowntimeListInVO inVO);


        List<GetOeeEquipmentDowntime> getOeeEquipmentDowntimeList(GetOeeDTO dto);

}
