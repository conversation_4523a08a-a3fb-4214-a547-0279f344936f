package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class GetProductBoxBarcodeInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工厂代码
     */
    @NotBlank(message = "工厂代码不能为空")
    private String companyCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 箱码
     */
    private String barcodeNo;

    /**
     * 状态  0 禁用   1 启用
     */
    private Integer barcodeStatus;


}
