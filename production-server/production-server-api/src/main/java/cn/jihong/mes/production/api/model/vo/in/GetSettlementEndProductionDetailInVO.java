package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/14 17:56
 */
@Data
public class GetSettlementEndProductionDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = -4195343414096144380L;


    /**
     * 生产工程单号
     */
    @NotBlank(message = "生产工程单号不能为空")
    private String productTicketNo;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String processName;

}
