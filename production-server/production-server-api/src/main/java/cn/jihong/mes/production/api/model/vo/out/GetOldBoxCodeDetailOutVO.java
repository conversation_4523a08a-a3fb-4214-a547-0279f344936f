package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetOldBoxCodeDetailOutVO implements Serializable {

    /**
     * 生产工程单号
     */
    private String planTicketNo;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 料号
     */
    private String materialCode;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;


    /**
     * 批次号
     */
    private String lotNo;


    /**
     * 箱数量
     */
    private Long boxNum;


    /**
     * 批次识别码
     */
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    private String machine;


    /**
     * 班次 01 02
     */
    private String shift;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    private String expirationDate;

    /**
     * 客户编号
     */
    private String customerNo;

    private String skuCode;

}
