package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/15 10:03
 */
@Data
public class GetSettlementWorkReportDetailInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = -1733050946855644296L;

    /**
     * 生产工程单号
     */
    @NotBlank(message = "生产工程单号不能为空")
    private String productTicketNo;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String defectiveProductsSourceName;


}
