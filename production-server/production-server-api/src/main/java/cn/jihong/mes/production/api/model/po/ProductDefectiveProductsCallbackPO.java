package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 不良品回调表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Getter
@Setter
@TableName("product_defective_products_callback")
public class ProductDefectiveProductsCallbackPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String PRODUCT_DEFECTIVE_ID = "product_defective_id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_NAME = "machine_name";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String DEFECTIVE_PRODUCTS_REASON = "defective_products_reason";
    public static final String DEFECTIVE_PRODUCTS_REASON_NAME = "defective_products_reason_name";
    public static final String DEFECTIVE_PRODUCTS_QUANTITY = "defective_products_quantity";
    public static final String CHECK_RESULT = "check_result";
    public static final String UNIT = "unit";
    public static final String DEFECTIVE_PRODUCTS_SOURCE = "defective_products_source";
    public static final String DEFECTIVE_PRODUCTS_SOURCE_NAME = "defective_products_source_name";
    public static final String PALLET_CODE = "pallet_code";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 不良品记录表id
     */
    @TableField(PRODUCT_DEFECTIVE_ID)
    private Long productDefectiveId;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;


    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;


    /**
     * 不良原因
     */
    @TableField(DEFECTIVE_PRODUCTS_REASON)
    private String defectiveProductsReason;


    /**
     * 不良原因名称
     */
    @TableField(DEFECTIVE_PRODUCTS_REASON_NAME)
    private String defectiveProductsReasonName;


    /**
     * 不良数量
     */
    @TableField(DEFECTIVE_PRODUCTS_QUANTITY)
    private BigDecimal defectiveProductsQuantity;


    /**
     * 校验结果 2 让步  3 重工  4  报废
     */
    @TableField(CHECK_RESULT)
    private Integer checkResult;


    /**
     * 单位
     */
    @TableField(UNIT)
    private String unit;


    /**
     * 不良来源
     */
    @TableField(DEFECTIVE_PRODUCTS_SOURCE)
    private String defectiveProductsSource;


    /**
     * 不良来源名称(工序名称)
     */
    @TableField(DEFECTIVE_PRODUCTS_SOURCE_NAME)
    private String defectiveProductsSourceName;


    /**
     * 栈板码
     */
    @TableField(PALLET_CODE)
    private String palletCode;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;


}
