package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.service.IJiHongService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 上栈板信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductLastPalletService extends IJiHongService<ProductPalletPO> {

    /**
     * 保存上料信息
     * @return: java.lang.String
     */
    String saveLastPalletInfo(SaveLastPalletInfoInVO saveLastPalletInfoInVO);

    /**
     * 物流上栈板
     *
     * @param saveLastPalletInfoInVO
     * @param meshType
     * @return
     */
    String saveIdePalletInfo(SaveLastPalletInfoInVO saveLastPalletInfoInVO, Integer meshType);



    /**
     * 根据栈板码查询上栈板信息
     */
    GetLastPalletByPalletCodeOutVO getLastPalletByPalletCode(GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO);


    /**
     * 查询上料列表
     */
    List<ProductPalletPO> getLastPalletList(List<Long> productTicketIdList,Integer status);

    /**
     * 查询使用中的上料列表
     */
    List<LastPalletOutVO> getLastPalletList(Long productTicket);


    /**
     * 查询成品（报工）信息
     * @param inVO
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetListByPalletSourceOutVO>
     * <AUTHOR>
     * @date: 2023/11/8 17:11
     */
    List<GetListByPalletSourceOutVO> getListByPalletSource(GetListByPalletSourceInVO inVO);

    /**
     * 查询上栈板记录
     */
    Pagination<LastPalletRecordsOutVO> getLastPalletRecords(ProductTicketPageInVO productTicketPageInVO);

    /**
     * 查询上栈板记录  -- 按照班组进行分组
     */
    Pagination<LastPalletRecordsOutVO> getLastPalletRecords(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    /**
     * 查询上栈板信息列表
     * @param pageNum
     * @param pageSize
     * @param productTickIdList
     * @param processName
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.po.ProductPalletPO>
     * <AUTHOR>
     * @date: 2023/11/15 10:07
     */
    Pagination<ProductPalletPO> getListByProductTickIds(Long pageNum, Long pageSize, List<Long> productTickIdList,String processName);


    void savePalletOperationRecords(BigDecimal originalQuantity, BigDecimal consumptionQuantity,
                                    BigDecimal remainingQuantity, String operationType, ProductTicketPO productTicketPO, Long productPalletId,
                                    String palletCode, Integer machineStopNo,String remainingReason);

    Pagination<PalletUseOutVO> getPalletUseList(GetPalletListPageInVO getPalletListPageInVO);

    PalletUseOutVO getPalletUseDetial(Long id);

    Pagination<PalletUseRecordOutVO> getPalletUseDetialList(ProductInfoPageInVO productInfoPageInVO);

    Long downPallet(DownMaterialInVO downMaterialInVO);

    Long temporaryStoragePallet(TemporaryStoragePalletInVO inVO);

    List<LastPalletOutVO> getLastPalletListByMachineName(GetByMachineNameInVO getByMachineNameInVO);

    ProductPalletPO getByPalletCode(String palletCode);

    List<LastPalletOutVO> getLastPalletListByMachineName(GetByMachineNameInVO getByMachineNameInVO,  List<Integer> status);

    /**
     * 查询暂存中的上栈板列表
     * @param getByMachineNameInVO
     * @return {@link List}<{@link LastPalletOutVO}>
     */
    List<LastPalletOutVO> getStagingLastPalletListByMachineName(GetByMachineNameInVO getByMachineNameInVO);

    /**
     * 修改栈板状态
     */
    Long updatePalletStatus(UpdatePalletStatusInVO updatePalletStatusInVO);

    /**
     * 查询上栈板信息
     * @param productTicketIds
     * @return
     */
    List<ProductPalletPO> getByProductTicketIds(List<Long> productTicketIds);


    ProductPalletPO getByOutboundId(Long outboundId);


    /**
     * 根据id获取栈板短码
     * @param id
     * @return
     */
    String getPalletShortCode(Long id);

    GetLastPalletByPalletCodeOutVO getPalletByPalletShortCode(GetLastPalletByPalletCodeInVO getLastPalletByPalletCodeInVO);

    Pagination<LastPalletRecordsOutVO> getDownPalletRecords(ProductTicketPageInVO productTicketPageInVO);
}
