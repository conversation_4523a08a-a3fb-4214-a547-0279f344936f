package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 栈板操作记录信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@Getter
@Setter
@TableName("product_pallet_operation_records")
public class ProductPalletOperationRecordsPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String PRODUCT_PALLET_ID = "product_pallet_id";
    public static final String PALLET_CODE = "pallet_code";
    public static final String OPERATION_TYPE = "operation_type";
    public static final String TICKET_TYPE = "ticket_type";
    public static final String TICKET_NUMBER = "ticket_number";
    public static final String OPERATOR = "operator";
    public static final String ORIGINAL_QUANTITY = "original_quantity";
    public static final String CONSUMPTION_QUANTITY = "consumption_quantity";
    public static final String REMAINING_QUANTITY = "remaining_quantity";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String MACHINE_STOP_NO = "machine_stop_no";
    public static final String REMAINING_REASON = "remaining_reason";
    public static final String REQUEST_ID = "request_id";


    @TableId(value = ID, type = IdType.AUTO)
    private Integer id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;

    /**
     * 栈板表id
     */
    @TableField(PRODUCT_PALLET_ID)
    private Long productPalletId;

    /**
     * 栈板码
     */
    @TableField(PALLET_CODE)
    private String palletCode;


    /**
     * 操作类型
     */
    @TableField(OPERATION_TYPE)
    private String operationType;


    /**
     * 工单类型
     */
    @TableField(TICKET_TYPE)
    private String ticketType;


    /**
     * 工单编号
     */
    @TableField(TICKET_NUMBER)
    private String ticketNumber;


    /**
     * 操作人员
     */
    @TableField(OPERATOR)
    private Long operator;


    /**
     * 原数量
     */
    @TableField(ORIGINAL_QUANTITY)
    private BigDecimal originalQuantity;


    /**
     * 消耗数量
     */
    @TableField(CONSUMPTION_QUANTITY)
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    @TableField(REMAINING_QUANTITY)
    private BigDecimal remainingQuantity;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 设备止码
     */
    @TableField(MACHINE_STOP_NO)
    private Integer machineStopNo;

    /**
     * 剩余原因
     */
    @TableField(REMAINING_REASON)
    private String remainingReason;


}
