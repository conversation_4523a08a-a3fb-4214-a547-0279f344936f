package cn.jihong.mes.production.api.model.vo.in.logistics;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-26 17:30
 */
@Data
public class CallMaterialInVO implements Serializable {


    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Long productTicketId;

    /**
     * erpOrderId (物流)
     */
    private List<String> erpOrderIdList;
}
