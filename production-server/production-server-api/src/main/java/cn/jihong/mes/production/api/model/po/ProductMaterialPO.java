package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 上料信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Getter
@Setter
@TableName("product_material")
public class ProductMaterialPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_NAME = "machine_name";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String MATERIAL_BARCODE_NO = "material_barcode_no";
    public static final String MATERIAL_CODE = "material_code";
    public static final String MATERIAL_TYPE = "material_type";
    public static final String MATERIAL_PLACE = "material_place";
    public static final String MATERIAL_PLACE_NAME = "material_place_name";
    public static final String MATERIAL_NAME = "material_name";
    public static final String MATERIAL_UNIT = "material_unit";
    public static final String MATERIAL_WAREHOUSING_TIME = "material_warehousing_time";
    public static final String LOADING_TIME = "loading_time";
    public static final String DOWN_TIME = "down_time";
    public static final String LOADING_QUANTITY = "loading_quantity";
    public static final String CONSUMPTION_QUANTITY = "consumption_quantity";
    public static final String REMAINING_QUANTITY = "remaining_quantity";
    public static final String REMAINING_REASON = "remaining_reason";
    public static final String STATUS = "status";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String MACHINE_STOP_NO = "machine_stop_no";
    public static final String PURCHASE_BATCH = "purchase_batch";
    public static final String WRITE_OFF_STATUS = "write_off_status";

    public static final String TRANSFER_NO = "transfer_no";
    public static final String DEDUCTION_NO = "deduction_no";
    public static final String PARTS = "parts";
    public static final String PARTS_NAME = "parts_name";
    public static final String WAREHOUSE_NO = "warehouse_no";
    public static final String WAREHOUSE_NAME = "warehouse_name";
    public static final String STORAGE_NO = "storage_no";
    public static final String STORAGE_NAME = "storage_name";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;


    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;


    /**
     * 物料编号
     */
    @TableField(MATERIAL_BARCODE_NO)
    private String materialBarcodeNo;


    /**
     * 物料code
     */
    @TableField(MATERIAL_CODE)
    private String materialCode;

    /**
     * 物料类别
     */
    @TableField(MATERIAL_TYPE)
    private String materialType;

    /**
     * 物料部件（用途）
     */
    @TableField(MATERIAL_PLACE)
    private String materialPlace;


    /**
     * 物料部件名称（用途名称）
     */
    @TableField(MATERIAL_PLACE_NAME)
    private String materialPlaceName;


    /**
     * 物料名称
     */
    @TableField(MATERIAL_NAME)
    private String materialName;


    /**
     * 物料单位
     */
    @TableField(MATERIAL_UNIT)
    private String materialUnit;


    /**
     * 物料入库时间
     */
    @TableField(MATERIAL_WAREHOUSING_TIME)
    private Date materialWarehousingTime;


    /**
     * 上料时间
     */
    @TableField(LOADING_TIME)
    private Date loadingTime;

    /**
     * 下料时间
     */
    @TableField(DOWN_TIME)
    private Date downTime;


    /**
     * 上料数量
     */
    @TableField(LOADING_QUANTITY)
    private BigDecimal loadingQuantity;


    /**
     * 物料消耗数量
     */
    @TableField(CONSUMPTION_QUANTITY)
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    @TableField(REMAINING_QUANTITY)
    private BigDecimal remainingQuantity;


    /**
     * 剩余原因
     */
    @TableField(REMAINING_REASON)
    private String remainingReason;


    /**
     * 状态
     */
    @TableField(STATUS)
    private Integer status;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 设备止码
     */
    @TableField(MACHINE_STOP_NO)
    private Integer machineStopNo;

    /**
     * 采购批次
     */
    @TableField(PURCHASE_BATCH)
    private String purchaseBatch;

    /**
     * 核销状态
     */
    @TableField(WRITE_OFF_STATUS)
    private Integer writeOffStatus;

    /**
     * 调拨单号
     */
    @TableField(TRANSFER_NO)
    private String transferNo;

    /**
     * 扣料单号
     */
    @TableField(DEDUCTION_NO)
    private String deductionNo;

    @TableField(PARTS)
    private String parts;

    @TableField(PARTS_NAME)
    private String partsName;

    @TableField(WAREHOUSE_NO)
    private String warehouseNo;

    @TableField(WAREHOUSE_NAME)
    private String warehouseName;

    @TableField(STORAGE_NO)
    private String storageNo;

    @TableField(STORAGE_NAME)
    private String storageName;

}
