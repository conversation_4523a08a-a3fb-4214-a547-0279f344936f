package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProcessTeamMembersVO implements Serializable {

    /**
     * 员工编号
     */
    private String userNo;

    /**
     * 员工名称
     */
    private String userName;
    /**
     * 员工职位编号
     */
    private String titleNo;
    /**
     * 员工职位名称
     */
    private String titleName;
    /**
     * 本级栈板码
     */
    private String onProcessBarCode;

    /**
     * 班次
     */
    private Integer shift;
    private String shiftName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

}
