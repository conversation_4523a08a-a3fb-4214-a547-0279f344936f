package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProductionMachineInVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * erp机器id
     */
    private String erpMachineId;

    /**
     * erp机台名称
     */
    private String erpMachineName;

    /**
     * erp设备名称
     */
    private String erpEquipmentName;

    /**
     * 调机时间
     */
    private Double debugMachineTime;

    /**
     * 结单时间
     */
    private Double statementTime;

    /**
     * 转产时间
     */
    private Double productionTime;

    /**
     * 新产时间
     */
    private Double newproductionTime;

    /**
     * 周保养时间
     */
    private Double weeklyMaintenanceTime;

    /**
     * 月保养时间
     */
    private Double monthlyMaintenanceTime;

    /**
     * 年度保养时间
     */
    private Double annualMaintenanceTime;

    /**
     * 机台配置
     */
    private ProductionMachineConfigVO productionMachineConfigVO;

    private ProductionMachineLogisticsConfigVO productionMachineLogisticsConfigVO;

    @Data
    public static class ProductionMachineConfigVO implements Serializable{
        private static final long serialVersionUID = 1L;

        /**
         * 是否需要机位： 0 不需要  1 需要
         */
        private Integer needMachineParts;

        /**
         * 结算类型：1 一单一结  2  一日一结
         */
        private Integer billingType;
    }

    @Data
    public static class ProductionMachineLogisticsConfigVO implements Serializable{
        private static final long serialVersionUID = 1L;

        /**
         * 车间编号
         */
        private String workshopCode;

        /**
         * 供料网带编号 网带编号:工序编号,网带编号:工序编号
         */
        private String supplyMeshCode;

        /**
         * 出口网带编号 网带编号:工序编号,网带编号:工序编号
         */
        private String exitMeshCode;

        /**
         * 备料网带数量
         */
        private Integer prepareMeshCount;

        /**
         * 出站类型  1 手动  2 自动
         */
        private Integer outboundType;


        /**
         * 备料网带编号
         */
        private String prepareMeshCode;

        /**
         * 上料网带编号
         */
        private String upMeshCode;


    }



}
