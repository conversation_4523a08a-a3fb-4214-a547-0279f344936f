package cn.jihong.mes.api.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-06-27 15:21
 */
@Data
public class EditProductionMachineOperationConfigInVO implements Serializable {

    /**
     * id
     */
    @NotNull(message = "机台id不能为空")
    private Long id;

    /**
     * 稼动类型 1:人工上报 2：设备上报
     */
    @NotNull(message = "稼动类型不能为空")
    private Integer operationType;

}
