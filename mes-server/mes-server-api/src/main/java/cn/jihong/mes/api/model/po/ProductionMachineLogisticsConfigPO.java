package cn.jihong.mes.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 机台物流相关属性
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@TableName("production_machine_logistics_config")
public class ProductionMachineLogisticsConfigPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_ID = "machine_id";
    public static final String WORKSHOP_CODE = "workshop_code";
    public static final String SUPPLY_MESH_CODE = "supply_mesh_code";
    public static final String EXIT_MESH_CODE = "exit_mesh_code";
    public static final String PREPARE_MESH_COUNT = "prepare_mesh_count";
    public static final String OUTBOUND_TYPE = "outbound_type";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String PREPARE_MESH_CODE = "prepare_mesh_code";
    public static final String UP_MESH_CODE = "up_mesh_code";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 据点
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 机台id
     */
    @TableField(MACHINE_ID)
    private Long machineId;


    /**
     * 车间编码
     */
    @TableField(value = WORKSHOP_CODE, updateStrategy = FieldStrategy.IGNORED)
    private String workshopCode;


    /**
     * 供料网带编号
     */
    @TableField(value = SUPPLY_MESH_CODE, updateStrategy = FieldStrategy.IGNORED)
    private String supplyMeshCode;


    /**
     * 出口网带编号
     */
    @TableField(EXIT_MESH_CODE)
    private String exitMeshCode;


    /**
     * 备料网带数量
     */
    @TableField(PREPARE_MESH_COUNT)
    private Integer prepareMeshCount;


    /**
     * 出站类型 出站类型  1 手动  2 自动
     */
    @TableField(OUTBOUND_TYPE)
    private Integer outboundType;

    /**
     * 备料网带编号
     */
    @TableField(PREPARE_MESH_CODE)
    private String prepareMeshCode;

    /**
     * 上料网带编号
     */
    @TableField(UP_MESH_CODE)
    private String upMeshCode;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Integer createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Integer updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
