package cn.jihong.mes.api.model.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

@Data
public class ActualLossStatisticsMesVO implements Serializable {
    /**
     * 完工日期
     */
    @ExcelProperty("完成月份")
    private String finishDate;

    /**
     * 产品编号
     */
    @ExcelIgnore
    private String productNo;

    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productName;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    private String workerOrderNo;

    /**
     * 产品类别编号
     */
    @ExcelIgnore
    private String productCategoryNo;

    /**
     * 产品类别名称
     */
    @ExcelProperty("产品类别")
    private String productCategoryName;

    /**
     * 工单数量
     */
    @ExcelProperty("工单数量")
    private BigDecimal workOrderQuantity;

    /**
     * 印刷良品数量
     */
    @ExcelProperty("印刷良品数量")
    private BigDecimal printGoodProductNum;

    /**
     * 印刷不良品数量
     */
    @ExcelProperty("印刷不良品数量")
    private BigDecimal printDefectiveProductNum;

    /**
     * 印刷损耗率
     */
    @ExcelProperty("印刷损耗率")
    private String printLossRate;

    /**
     * 分条良品数量
     */
    @ExcelProperty("分条良品数量")
    private BigDecimal stripingGoodProductNum;

    /**
     * 分条不良品数量
     */
    @ExcelProperty("分条不良品数量")
    private BigDecimal stripingDefectiveProductNum;

    /**
     * 分条损耗率
     */
    @ExcelProperty("分条损耗率")
    private String stripingRate;

    /**
     * 平张良品数量
     */
    @ExcelProperty("平张良品数量")
    private BigDecimal flatSheetGoodProductNum;

    /**
     * 平张不良品数量
     */
    @ExcelProperty("平张不良品数量")
    private BigDecimal flatSheetDefectiveProductNum;


    /**
     * 平张损耗率
     */
    @ExcelProperty("平张损耗率")
    private String flatSheetLossRate;

    /**
     * 模切良品数量
     */
    @ExcelProperty("模切良品数量")
    private BigDecimal dieCutGoodProductNum;

    /**
     * 模切不良品数量
     */
    @ExcelProperty("模切不良品数量")
    private BigDecimal dieCutDefectiveProductNum;

    /**
     * 模切损耗率
     */
    @ExcelProperty("模切损耗率")
    private String dieCutRate;

    /**
     * 裁切/分切/成型/糊盒良品数量
     */
    @ExcelProperty("裁切/分切/成型/糊盒良品数量")
    private BigDecimal otherGoodProductNum;



    /**
     * 裁切/分切/成型/糊盒不
     *
     *
     * 良品数量
     */
    @ExcelProperty("裁切/分切/成型/糊盒不良品数量")
    private BigDecimal otherDefectiveProductNum;



    /**
     * 裁切/分切/成型/糊盒损耗率
     *
     */
    @ExcelProperty("裁切/分切/成型/糊盒损耗率")
    private String otherLossRate;

    /**
     * 已入库合格数量
     */
    @ExcelProperty("已入库合格数量")
    private BigDecimal qualifiedQuantity;

    /**
     * 不良品总数
     */
    @ExcelProperty("不良品总数")
    private BigDecimal defectiveProductNum;


    /**
     * 总损耗率
     */
    @ExcelProperty("总损耗率")
    private String totalLossRate;
}
