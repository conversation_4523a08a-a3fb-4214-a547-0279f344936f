package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProductionMachineOutVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 工序
     */
    private String process;

    /**
     * 工序CODE
     */
    private String processCode;

    /**
     * erp机台id
     */
    private String erpMachineId;

    /**
     * erp机台名称
     */
    private String erpMachineName;

    /**
     * erp设备
     */
    private String erpEquipmentName;

    /**
     * 结单时间
     */
    private Double statementTime;

    /**
     * 转产时间
     */
    private Double productionTime;

    /**
     * 新产时间
     */
    private Double newproductionTime;


    /**
     * 调机时间
     */
    private Double debugMachineTime;

    /**
     * 周保养时间
     */
    private Double weeklyMaintenanceTime;

    /**
     * 月保养时间
     */
    private Double monthlyMaintenanceTime;

    /**
     * 年度保养时间
     */
    private Double annualMaintenanceTime;

    /**
     * 企业编号
     */
    private String enterpriseNumbers;

    /**
     * 机台编号
     */
    private String machineNumber;

    /**
     * 机台车间
     */
    private String workPlace;

    /**
     * 机台配置
     */
    private ProductionMachineInVO.ProductionMachineConfigVO productionMachineConfigVO;


    /**
     * 供料网带编号
     */
    private String supplyMeshId;

    /**
     * 出口网带编号
     */
    private String exitMeshId;

    /**
     * 车间编码
     */
    private String workshopCode;

    /**
     *  网带类型（1：供料  2：出口）
     */
    private Integer meshType;

}
