package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.ProcessBarCodeDTO;
import cn.jihong.mes.api.model.dto.ProcessTeamMembersDTO;
import cn.jihong.mes.api.model.dto.TblCusProcessBarCodeHistoryDTO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodeHistoryPO;
import cn.jihong.mes.api.model.vo.ProcessDetailVO;
import cn.jihong.mes.api.model.vo.ProcessTeamMembersVO;
import cn.jihong.mes.api.model.vo.ProductionProcessesVO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeHistoryVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface ITblCusProcessBarCodeHistoryService extends IJiHongService<TblCusProcessBarCodeHistoryPO> {

    List<TblCusProcessBarCodeHistoryVO> getTblCusProcessBarCodeHistoryList(TblCusProcessBarCodeHistoryDTO tblCusProcessBarCodeHistoryDTO);

    List<ProcessTeamMembersVO> getProcessTeamMembers(ProcessTeamMembersDTO processTeamMembersDTO);


    List<ProcessDetailVO> getProcessDetail(ProcessTeamMembersDTO processTeamMembersDTO);


    List<TblCusProcessBarCodeHistoryVO> recursionTblCusProcessBarCodeHistoryListByChildOnProcessBarCode(ProcessBarCodeDTO processBarCodeDTO);

    List<ProductionProcessesVO> getProductionProcessesVOs(ProcessBarCodeDTO processBarCodeDTO);

}
