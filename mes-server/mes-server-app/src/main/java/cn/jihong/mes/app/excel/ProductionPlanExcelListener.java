package cn.jihong.mes.app.excel;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ProductionPlanDTO;
import cn.jihong.mes.api.model.dto.TblwipdispatchstateDTO;
import cn.jihong.mes.api.model.enums.PlanTypeEnum;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.api.model.po.UpkeepExecutePO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.model.vo.ProductionShiftDetailVO;
import cn.jihong.mes.api.model.vo.ProductionShiftSetVO;
import cn.jihong.mes.api.model.vo.TimeVO;
import cn.jihong.mes.api.service.*;
import cn.jihong.mes.app.enums.MachineWorkHoursEnum;
import cn.jihong.oa.erp.api.model.enums.UnitEnum;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByProcessNameOutVO;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ProductionPlanExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    // 正则表达式匹配中文字符
    private static final String REGEX = "[\\u4e00-\\u9fa5]";
    private List<Map<Integer, String>> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    private List<ProductionPlanDTO> productionPlanDTOList = new ArrayList<>();
    private Map<Integer,String> excelHead = new HashMap<>();
    private Map<Integer,String> excelWeek = new HashMap<>();
    private Map<Integer,String> machineMap = new HashMap<>();

    private IOocqlTService iOocqlTService;

    private ISfcbTService sfcbTService;

    public ProductionPlanExcelListener(IOocqlTService iOocqlTService,ISfcbTService sfcbTService) {
        this.iOocqlTService = iOocqlTService;
        this.sfcbTService = sfcbTService;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        //解析表头
        if ("序号".equals(data.get(0))){
            excelHead = data;
            return;
        }
        if (data.get(19) != null && data.get(19).contains("星期")){
            excelWeek = data;
            return;
        }
        //解析工序+机器名
        if (data.get(2)== null && data.get(3) == null && data.get(4) == null && data.get(5) == null){
//            ProductionPlanDTO productionPlanDTO = new ProductionPlanDTO();
//            productionPlanDTO.setProductionProcess(data.get(0));
//            productionPlanDTO.setProductionMachine(data.get(1));
//            productionPlanDTOList.add(productionPlanDTO);
            machineMap.put(0,data.get(1));
            return;
        }
        //解析计划产量行数据
        ProductionPlanDTO productionPlanDTO = new ProductionPlanDTO();
        productionPlanDTO.setCompanyId(SecurityUtil.getCompanyId() == null?null:SecurityUtil.getCompanyId().intValue());
        for (int i=0 ;i<data.size();i++){
            switch (i){
                case 0:
                    //序号
                    break;
                case 1:
                    //产品名称
                    productionPlanDTO.setProductionName(data.get(i));
                    break;
                case 2:
                    //工序
                    productionPlanDTO.setProductionProcess(data.get(i));
                    break;
                case 3:
                    //工单号
                    productionPlanDTO.setWorkerOrderNo(data.get(i));
                    break;
                case 16:
                    //单位
                    String unit = data.get(i);
                    if (StrUtil.isEmpty(unit)){
                        throw new CommonException("单位不能为空");
                    }
                    productionPlanDTO.setUnit(data.get(i));
                    break;
                case 17:
                    //标准产能/H
                    String standardProductionCapacity = data.get(i);
                    if (StrUtil.isEmpty(standardProductionCapacity)){
                        throw new CommonException("标准产能不能为空");
                    }
                    productionPlanDTO.setStandardProductionCapacity(Double.valueOf(data.get(i)));
                    break;
                case 18:
                    //换产时间/H
                    String productionChangeHours = data.get(i);
                    if (StrUtil.isEmpty(productionChangeHours)){
                        throw new CommonException("换产时间不能为空");
                    }
                    productionPlanDTO.setProductionChangeHours(Double.valueOf(data.get(i)));
                    break;
                default:
                    break;

            }
            if (i >= 19){
                IProductionShiftSetService productionShiftSetService = SpringUtil.getBean("productionShiftSetServiceImpl");
                int aa = i +1;
                if (excelHead.get(i)==null|| "".equals(excelHead.get(i).trim())){

                    throw new CommonException("第"+aa+"列有多余数据，请整列删除，注意不是清空数据");
                }
                ProductionShiftSetVO productionShiftSetVO = productionShiftSetService.getOneByDate(excelHead.get(i));
                if (productionShiftSetVO == null){
                    throw new CommonException("第"+aa+"列该生产计划时间未设定班次");
                }
                IProductionShiftDetailService productionShiftDetailService = SpringUtil.getBean("productionShiftDetailServiceImpl");
                List<ProductionShiftDetailVO> productionShiftDetailVOList = productionShiftDetailService.getDetailList(productionShiftSetVO.getId().toString());
                Double sumAll = 0.0;
                for (ProductionShiftDetailVO productionShiftDetailVO:productionShiftDetailVOList){
                    List<TimeVO> timeStr = productionShiftDetailVO.getTimeSlot();
                    for (int j=0;j<timeStr.size();j++){
                        sumAll+=Math.abs(DateUtil.dateDiff(DateUtil.parseStringToDateCustom(timeStr.get(j).getStartTime(),"HH:mm"),DateUtil.parseStringToDateCustom(timeStr.get(j).getEndTime(),"HH:mm"))/3600000.0);
                    }
                    List<String> timeList = new ArrayList<>();

                }
                //计划产量解析
                String capStr = data.get(i);
                log.debug("产量解析数据为："+capStr);
                if (StringUtil.isNotEmpty(capStr) && !"null".equals(capStr)){
                    List<String> capList = Arrays.asList(capStr.trim().replaceAll(REGEX, "").split("/"));
                    //处理计划产量单元格数据格式为：中文/中文 的场景
                    if (CollectionUtil.isEmpty(capList)){
                        continue;
                    }
//                    int a = 0;
                    int b = 0;
                    Date timeHead = null;//班次开始时间
                    String machineName = machineMap.get(0);
                    if (StrUtil.isEmpty(machineName)){
                        throw new CommonException("没有读取到对应的机台名称，请检查并重新导入!");
                    }
                    String[] machineNameArr = machineName.split("：");
                    String shift = null;
                    if (machineNameArr != null && machineNameArr.length>1){
                        shift = machineNameArr[1];
                    }
                    if (shift != null && "1班".equals(shift) && capList.size()>1){
                        throw new CommonException("只有一个班次，计划产量数据请不要用斜线分隔");
                    }
                    for (ProductionShiftDetailVO productionShiftDetailVO:productionShiftDetailVOList){
                        if (shift != null && "2班".equals(shift) && b>1){
                            break;
                        }
                        if (LocalDate.now().isAfter(DateUtil.parseStringToDateCustom(excelHead.get(i),"yyyy/MM/dd").toInstant().atZone(ZoneId.systemDefault()).toLocalDate())){
                            break;
                        }
                        List<TimeVO> timeStr = productionShiftDetailVO.getTimeSlot();
//                        Double sumTime = 0.0;
//                        for (TimeVO ts:timeStr) {
//                            Double timeDiff = DateUtil.dateDiff(DateUtil.parseStringToDateCustom(ts.getStartTime(), "HH:mm"), DateUtil.parseStringToDateCustom(ts.getEndTime(), "HH:mm")) / 3600000.0;
//                            sumTime += Math.abs(timeDiff);
//                        }
//                        Double beforeTimeDiff = 0.0;
                        String startTime = timeStr.get(0).getStartTime();
                        String endTime = timeStr.get(timeStr.size()-1).getEndTime();
//                        for (TimeVO ts:timeStr){
//                            if(b==0){
//                                String timeHeadStr = ts.getStartTime();
//                                timeHead = DateUtil.parseStringToDateCustom(timeHeadStr,"HH:mm");
//                            }
//                            Double timeDiff = DateUtil.dateDiff(DateUtil.parseStringToDateCustom(ts.getStartTime(),"HH:mm"),DateUtil.parseStringToDateCustom(ts.getEndTime(),"HH:mm"))/3600000.0;
//                            timeDiff = Math.abs(timeDiff);
                        ProductionPlanDTO productionPlanDTONew = new ProductionPlanDTO();
                        BeanUtil.copyProperties(productionPlanDTO,productionPlanDTONew);
                        productionPlanDTONew.setProductionPlanDate(DateUtil.parseStringToDateCustom(excelHead.get(i),"yyyy/MM/dd"));
                        productionPlanDTONew.setWeekDay(excelWeek.get(i));
                        productionPlanDTONew.setSerialNo(productionShiftDetailVO.getSerialNo());


                        productionPlanDTONew.setProductionPlanStartTime(DateUtil.parseStringToDateCustom(excelHead.get(i)+" "+startTime,"yyyy/MM/dd HH:mm"));
                        //晚班结束时间日期+1天
                        if ((shift != null && "2班".equals(shift) && b>0) || b>0 ){
                            productionPlanDTONew.setProductionPlanEndTime(DateUtil.addDays(DateUtil.parseStringToDateCustom(excelHead.get(i)+" "+endTime,"yyyy/MM/dd HH:mm"),1));
                        }else {
                            productionPlanDTONew.setProductionPlanEndTime(DateUtil.parseStringToDateCustom(excelHead.get(i)+" "+endTime,"yyyy/MM/dd HH:mm"));
                        }

                        productionPlanDTONew.setProductionMachine(machineNameArr[0]);
                        IProductionMachineService productionMachineService = SpringUtil.getBean("productionMachineServiceImpl");
                        ProductionMachineOutVO productionMachineOutVO = productionMachineService.getProductionMachineByName(machineNameArr[0],productionPlanDTO.getProductionProcess());
                        if (productionMachineOutVO == null) {
                            throw new CommonException("查询不到工序为"+ productionPlanDTO.getProductionProcess() +"的机台" + machineNameArr[0]);
                        }
                        productionPlanDTONew.setProductionMachineNo(productionMachineOutVO.getErpMachineId());
                        productionPlanDTONew.setErpMachineName(productionMachineOutVO.getErpMachineName());
                        log.debug("capList size:"+capList);
                        if (capList.size()<=1 && b > 0){
                            break;
                        }
                        productionPlanDTONew.setIsDeleted(false);
                        productionPlanDTONew.setCompanyId(SecurityUtil.getCompanyId() == null ? null:Math.toIntExact(SecurityUtil.getCompanyId()));
                        String cap = capList.get(b);
                        try {
                            if (cap.contains("|")){
                                ProductionPlanDTO productionPlanDTOAdd = new ProductionPlanDTO();
                                BeanUtil.copyProperties(productionPlanDTONew,productionPlanDTOAdd);
                                String[] beforeStr = cap.split("\\|");
                                if (MachineWorkHoursEnum.CHANGE_TIME.getCode().equals(beforeStr[0])){
                                    productionPlanDTOAdd.setPlanType(PlanTypeEnum.CHANGE_PRODUCTION.getCode());
                                }else if (MachineWorkHoursEnum.STATEMENT_TIME.getCode().equals(beforeStr[0])){
                                    productionPlanDTOAdd.setPlanType(PlanTypeEnum.STATEMENT_ACCOUNT.getCode());
                                }else if(MachineWorkHoursEnum.REDO.getCode().equals(beforeStr[0])){
                                    productionPlanDTOAdd.setPlanType(PlanTypeEnum.REDO.getCode());
                                }
                                productionPlanDTOList.add(productionPlanDTOAdd);
                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(beforeStr[1]));
                            }else {
                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(cap));
                            }
                        }catch (Exception e){
                            log.error("产量数据为："+capList.get(b));
                            productionPlanDTONew.setPlannedProductionCapacity(0.0);
                        }

//                            if (StringUtil.isNotEmpty(data.get(i))){
//                                将一天的计划产量分摊到各个班次
//                                if (capList.get(a).contains("(")){
//                                    String beforeStr = capList.get(a).substring(capList.get(a).indexOf("(") + 1, capList.get(a).indexOf(")"));
//                                    String afterStr = capList.get(a).substring(capList.get(a).indexOf(")") + 1);
//
//                                    if (MachineWorkHoursEnum.STATEMENT_TIME.getCode().equals(beforeStr)){
//                                        Double statementTime = productionMachineOutVO.getStatementTime();
//                                        if (statementTime != null){
//                                            if (beforeTimeDiff == 0.0 && statementTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-statementTime)/(sumTime-statementTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && statementTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > statementTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-statementTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < statementTime && (beforeTimeDiff + timeDiff) < statementTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < statementTime && (beforeTimeDiff + timeDiff) > statementTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-statementTime)/(sumTime-statementTime));
//                                            }
//                                        }
//                                    }else if (MachineWorkHoursEnum.PRODUCTION_TIME.getCode().equals(beforeStr)){
//                                        Double productionTime = productionMachineOutVO.getProductionTime();
//                                        if (productionTime != null){
//                                            if (beforeTimeDiff == 0.0 && productionTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-productionTime)/(sumTime-productionTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && productionTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > productionTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-productionTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < productionTime && (beforeTimeDiff + timeDiff) < productionTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < productionTime && (beforeTimeDiff + timeDiff) > productionTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-productionTime)/(sumTime-productionTime));
//                                            }
//                                        }
//                                    }else if (MachineWorkHoursEnum.NEWPRODUCTION_TIME.getCode().equals(beforeStr)){
//                                        Double newproductionTime = productionMachineOutVO.getNewproductionTime();
//                                        if (newproductionTime != null){
//                                            if (beforeTimeDiff == 0.0 && newproductionTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-newproductionTime)/(sumTime-newproductionTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && newproductionTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > newproductionTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-newproductionTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < newproductionTime && (beforeTimeDiff + timeDiff) < newproductionTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < newproductionTime && (beforeTimeDiff + timeDiff) > newproductionTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-newproductionTime)/(sumTime-newproductionTime));
//                                            }
//                                        }
//                                    }else if (MachineWorkHoursEnum.DEBUG_MACHINE_TIME.getCode().equals(beforeStr)){
//                                        Double debugMachineTime = productionMachineOutVO.getDebugMachineTime();
//                                        if (debugMachineTime != null){
//                                            if (beforeTimeDiff == 0.0 && debugMachineTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-debugMachineTime)/(sumTime-debugMachineTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && debugMachineTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > debugMachineTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-debugMachineTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < debugMachineTime && (beforeTimeDiff + timeDiff) < debugMachineTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < debugMachineTime && (beforeTimeDiff + timeDiff) > debugMachineTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-debugMachineTime)/(sumTime-debugMachineTime));
//                                            }
//                                        }
//                                    }else if (MachineWorkHoursEnum.WEEKLY_MAINTENANCE_TIME.getCode().equals(beforeStr)){
//                                        Double weeklyMaintenanceTime = productionMachineOutVO.getWeeklyMaintenanceTime();
//                                        productionPlanDTONew.setUpkeepType(MachineWorkHoursEnum.WEEKLY_MAINTENANCE_TIME.getCode());
//                                        productionPlanDTONew.setUpkeepTime(weeklyMaintenanceTime);
//                                        if (weeklyMaintenanceTime != null){
//                                            if (beforeTimeDiff == 0.0 && weeklyMaintenanceTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-weeklyMaintenanceTime)/(sumTime-weeklyMaintenanceTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && weeklyMaintenanceTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > weeklyMaintenanceTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-weeklyMaintenanceTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < weeklyMaintenanceTime && (beforeTimeDiff + timeDiff) < weeklyMaintenanceTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < weeklyMaintenanceTime && (beforeTimeDiff + timeDiff) > weeklyMaintenanceTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-weeklyMaintenanceTime)/(sumTime-weeklyMaintenanceTime));
//                                            }
//                                        }
//                                    }else if (MachineWorkHoursEnum.MONTHLY_MAINTENANCE_TIME.getCode().equals(beforeStr)){
//                                        Double monthlyMaintenanceTime = productionMachineOutVO.getMonthlyMaintenanceTime();
//                                        productionPlanDTONew.setUpkeepType(MachineWorkHoursEnum.MONTHLY_MAINTENANCE_TIME.getCode());
//                                        productionPlanDTONew.setUpkeepTime(monthlyMaintenanceTime);
//                                        if (monthlyMaintenanceTime != null){
//                                            if (beforeTimeDiff == 0.0 && monthlyMaintenanceTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-monthlyMaintenanceTime)/(sumTime-monthlyMaintenanceTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && monthlyMaintenanceTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > monthlyMaintenanceTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-monthlyMaintenanceTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < monthlyMaintenanceTime && (beforeTimeDiff + timeDiff) < monthlyMaintenanceTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < monthlyMaintenanceTime && (beforeTimeDiff + timeDiff) > monthlyMaintenanceTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-monthlyMaintenanceTime)/(sumTime-monthlyMaintenanceTime));
//                                            }
//                                        }
//                                    }else if (MachineWorkHoursEnum.ANNUAL_MAINTENANCE_TIME.getCode().equals(beforeStr)){
//                                        Double annualMaintenanceTime = productionMachineOutVO.getAnnualMaintenanceTime();
//                                        productionPlanDTONew.setUpkeepType(MachineWorkHoursEnum.ANNUAL_MAINTENANCE_TIME.getCode());
//                                        productionPlanDTONew.setUpkeepTime(annualMaintenanceTime);
//                                        if (annualMaintenanceTime != null){
//                                            if (beforeTimeDiff == 0.0 && annualMaintenanceTime<timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff-annualMaintenanceTime)/(sumTime-annualMaintenanceTime));
//
//                                            }else if (beforeTimeDiff == 0.0 && annualMaintenanceTime > timeDiff){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff > annualMaintenanceTime){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*timeDiff/(sumTime-annualMaintenanceTime));
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < annualMaintenanceTime && (beforeTimeDiff + timeDiff) < annualMaintenanceTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(0.0);
//                                            }else if (beforeTimeDiff > 0.0 && beforeTimeDiff < annualMaintenanceTime && (beforeTimeDiff + timeDiff) > annualMaintenanceTime ){
//                                                productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(afterStr)*(timeDiff+beforeTimeDiff-annualMaintenanceTime)/(sumTime-annualMaintenanceTime));
//                                            }
//                                        }
//                                    }else {
//                                        throw new CommonException(excelHead.get(i)+"的计划产量的数据为："+data.get(i)+"格式有误，请修正后重新导入");
//                                    }
//                                    beforeTimeDiff+=timeDiff;
//                                }else {
//                                    productionPlanDTONew.setPlannedProductionCapacity(Double.valueOf(capList.get(a))*timeDiff/sumTime);
//
//                                }

//                            }
                        productionPlanDTONew.setPlanType(PlanTypeEnum.ON_PRODUCE.getCode());
                        productionPlanDTOList.add(productionPlanDTONew);
                        if (shift != null && "1班".equals(shift) && b==0){
                            break;
                        }
                        b++;

//                        a++;
                    }
                }

            }

        }
    }


    /***
     * @description: 保存保养执行情况
     * @return: void
     * <AUTHOR>
     * @date: 2023/9/5 16:03
     */
    public void saveUpkeepExecute(){
        IUpkeepExecuteService iUpkeepExecuteService =  SpringUtil.getBean("upkeepExecuteServiceImpl");
        List<UpkeepExecutePO> upkeepExecuteTempList = BeanUtil.copyToList(productionPlanDTOList,UpkeepExecutePO.class);
        List<UpkeepExecutePO> upkeepExecutePOS = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        for(UpkeepExecutePO po : upkeepExecuteTempList){
            if(Objects.nonNull(po.getUpkeepType())){
                if(!list.contains(po.getProductionPlanDate())){
                    upkeepExecutePOS.add(po);
                    list.add(po.getProductionPlanDate());
                }
            }
        }
        iUpkeepExecuteService.saveBatch(upkeepExecutePOS);
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
//        saveUpkeepExecute();
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("开始存储数据库！");
        List<ProductionPlanPO> productionPlanPOS = BeanUtil.copyToList(productionPlanDTOList,ProductionPlanPO.class);
        Map<String,String> unitMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(productionPlanPOS)){
            // 工厂代码 和 工序名称 去erp查询相关的工序类型
            String companyCode = productionPlanPOS.get(0).getProductionMachineNo().split(":")[1];
            List<String> processNameList = productionPlanPOS.stream().map(ProductionPlanPO::getProductionProcess).collect(Collectors.toList());
            List<GetOocqlTByProcessNameOutVO> list = iOocqlTService.getOocqlTByProcessName(companyCode, processNameList);
            for (GetOocqlTByProcessNameOutVO getOocqlTByProcessNameOutVO:list){
                if (StringUtils.isEmpty(getOocqlTByProcessNameOutVO.getEcaauc009())){
                    throw new CommonException("据点："+getOocqlTByProcessNameOutVO.getEcaaucsite()+"工序："+getOocqlTByProcessNameOutVO.getOocql004()+"尚未在ERP维护工序单位");
                }
                if (StringUtils.isEmpty(getOocqlTByProcessNameOutVO.getOocq026())){
                    throw new CommonException("据点："+getOocqlTByProcessNameOutVO.getEcaaucsite()+"工序："+getOocqlTByProcessNameOutVO.getOocql004()+"尚未在ERP维护工序类型");
                }
            }
            Map<String, String> map = list.stream().collect(Collectors.toMap(GetOocqlTByProcessNameOutVO::getOocql004, GetOocqlTByProcessNameOutVO::getOocq026));
            //工序单位map
            unitMap = list.stream().collect(Collectors.toMap(GetOocqlTByProcessNameOutVO::getOocql004,GetOocqlTByProcessNameOutVO::getEcaauc009));

            // 校验工序类型是否为空
//            List<String> blankProcessTypeList = new ArrayList<>();
//            for (Map.Entry<String, String> entry : map.entrySet()) {
//                if (StringUtils.isBlank(entry.getValue())) {
//                    blankProcessTypeList.add(entry.getKey());
//                }
//            }
//            if(CollectionUtil.isNotEmpty(blankProcessTypeList)){
//                throw new CommonException("工序的工序类型尚未在ERP维护：" + JSON.toJSONString(blankProcessTypeList));
//            }
            productionPlanPOS = productionPlanPOS.stream().peek(t->{
                t.setProductionProcessType(map.get(t.getProductionProcess()));
            }).collect(Collectors.toList());

            //校验工序是否正确
            List<String> workerOrderNos = productionPlanPOS.stream().map(ProductionPlanPO::getWorkerOrderNo).collect(Collectors.toList());
            Map<String, Set<String>> processMap = sfcbTService.getProcessByTickNos(workerOrderNos);

            for (ProductionPlanPO productionPlanPO:productionPlanPOS){
                //校验单位是否正确
                if (!unitMap.containsKey(productionPlanPO.getProductionProcess()) || StrUtil.isEmpty(unitMap.get(productionPlanPO.getProductionProcess()))){
                    throw new CommonException("机台名称："+productionPlanPO.getProductionMachine()+" 工序："+productionPlanPO.getProductionProcess()+" 产品名称:"+productionPlanPO.getProductionName()+" 工单号："+productionPlanPO.getWorkerOrderNo()+"的单位尚未在ERP维护");
                }
                if(!productionPlanPO.getUnit().equals(UnitEnum.getUnitEnum(unitMap.get(productionPlanPO.getProductionProcess())).getName())){
                    log.debug("导入的单位："+productionPlanPO.getUnit());
                    log.debug("ERP的工序单位:"+UnitEnum.getUnitEnum(unitMap.get(productionPlanPO.getProductionProcess())).getName());
                    throw new CommonException("机台名称："+productionPlanPO.getProductionMachine()+" 工序："+productionPlanPO.getProductionProcess()+" 产品名称:"+productionPlanPO.getProductionName()+" 工单号："+productionPlanPO.getWorkerOrderNo()+"的单位请修改为："+UnitEnum.getUnitEnum(unitMap.get(productionPlanPO.getProductionProcess())).getName());
                }

                //校验工序是否正确
                if (processMap != null && CollectionUtil.isNotEmpty(processMap.get(productionPlanPO.getWorkerOrderNo()))){
                    Set<String> processSet = processMap.get(productionPlanPO.getWorkerOrderNo());
                    if(!processSet.contains(productionPlanPO.getProductionProcess())){
                        throw new CommonException("机台名称："+productionPlanPO.getProductionMachine()+" 产品名称:"+productionPlanPO.getProductionName()+" 工单号："+productionPlanPO.getWorkerOrderNo()+"的工序名称应为如下的一种："+String.join(",", processSet));
                    }
                }
            }
        }

        IProductionPlanService productionPlanService = SpringUtil.getBean("productionPlanServiceImpl");
        productionPlanService.addBatch(productionPlanPOS);
        log.info("存储数据库成功！");

        //派工
//        List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList = new ArrayList<>();
//        for (ProductionPlanPO productionPlanPO:productionPlanPOS){
//            TblwipdispatchstateDTO tblwipdispatchstateDTO = new TblwipdispatchstateDTO();
//            String productionMachineId = productionPlanPO.getProductionMachineNo();
//            if (StringUtil.isNotEmpty(productionMachineId)){
//                List<String> productionMachineIdList  = Arrays.asList(productionMachineId.split(":"));
//                String productionMachineNo = productionMachineIdList.get(productionMachineIdList.size()-1);
//                tblwipdispatchstateDTO.setEquipmentno(productionMachineNo);
//            }
//            if (StringUtil.isEmpty(productionPlanPO.getWorkerOrderNo())){
//                continue;
//            }
//            ITblopbasisService tblopbasisService = SpringUtil.getBean("tblopbasisServiceImpl");
//            TblopbasisVO tblopbasisVO = tblopbasisService.getVOByName(productionPlanPO.getProductionProcess());
//            tblwipdispatchstateDTO.setReviser(SecurityUtil.getWorkcode());
//            tblwipdispatchstateDTO.setRevisedate(new Date());
//            tblwipdispatchstateDTO.setOpno(tblopbasisVO.getOpno());
//            tblwipdispatchstateDTO.setLotno(productionPlanPO.getWorkerOrderNo()+"-001");
//            tblwipdispatchstateDTO.setWorkdate(productionPlanPO.getProductionPlanDate());
//            tblwipdispatchstateDTO.setDispStartTime(productionPlanPO.getProductionPlanStartTime());
//            tblwipdispatchstateDTO.setDispEndTime(productionPlanPO.getProductionPlanEndTime());
//            tblwipdispatchstateDTO.setStdDispStartTime(productionPlanPO.getProductionPlanStartTime());
//            tblwipdispatchstateDTO.setStdDispEndTime(productionPlanPO.getProductionPlanEndTime());
//            tblwipdispatchstateDTO.setQty(productionPlanPO.getPlannedProductionCapacity());
//            tblwipdispatchstateDTO.setSeq(1);
//            tblwipdispatchstateDTOList.add(tblwipdispatchstateDTO);
//        }
//        dispatching(tblwipdispatchstateDTOList);
    }

    private Date nowDay(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        return zero;
    }

    /**
     * 派工
     * @param tblwipdispatchstateDTOList
     */
    private void dispatching(List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList){
        ITblwipdispatchstateService tblwipdispatchstateService = SpringUtil.getBean("tblwipdispatchstateServiceImpl");
        List<TblwipdispatchstateDTO> tblwipdispatchstateDTOS = tblwipdispatchstateService.getListByLotNos(tblwipdispatchstateDTOList.stream().map(TblwipdispatchstateDTO::getLotno).collect(Collectors.toList()));
        Map<String,TblwipdispatchstateDTO> lotNoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(tblwipdispatchstateDTOS)){
            for (TblwipdispatchstateDTO tblwipdispatchstateDTO:tblwipdispatchstateDTOS){
                lotNoMap.put(tblwipdispatchstateDTO.getOpno()+tblwipdispatchstateDTO.getEquipmentno()+tblwipdispatchstateDTO.getLotno()+tblwipdispatchstateDTO.getSeq(),tblwipdispatchstateDTO);
            }
        }
        List<TblwipdispatchstateDTO> tblwipdispatchstateDTOListNew = new ArrayList<>();
        List<TblwipdispatchstateDTO> tblwipdispatchstateDTOListUpdate = new ArrayList<>();
        for (TblwipdispatchstateDTO tblwipdispatchstateDTO:tblwipdispatchstateDTOList){
            if (!lotNoMap.containsKey(tblwipdispatchstateDTO.getOpno()+tblwipdispatchstateDTO.getEquipmentno()+tblwipdispatchstateDTO.getLotno()+tblwipdispatchstateDTO.getSeq())){
                tblwipdispatchstateDTOListNew.add(tblwipdispatchstateDTO);
            }else {
                TblwipdispatchstateDTO tblwipdispatchstateDTOUpdate = lotNoMap.get(tblwipdispatchstateDTO.getOpno()+tblwipdispatchstateDTO.getEquipmentno()+tblwipdispatchstateDTO.getLotno()+tblwipdispatchstateDTO.getSeq());
                tblwipdispatchstateDTOUpdate.setRevisedate(new Date());
                tblwipdispatchstateDTOUpdate.setReviser(SecurityUtil.getWorkcode());
                tblwipdispatchstateDTOUpdate.setDispEndTime(tblwipdispatchstateDTO.getDispEndTime());
                tblwipdispatchstateDTOUpdate.setDispStartTime(tblwipdispatchstateDTO.getDispStartTime());
                tblwipdispatchstateDTOUpdate.setStdDispEndTime(tblwipdispatchstateDTO.getStdDispEndTime());
                tblwipdispatchstateDTOUpdate.setStdDispStartTime(tblwipdispatchstateDTO.getStdDispStartTime());
                tblwipdispatchstateDTOUpdate.setQty(tblwipdispatchstateDTO.getQty());
                tblwipdispatchstateDTOListUpdate.add(tblwipdispatchstateDTOUpdate);
            }
        }
        tblwipdispatchstateService.saveBatch(tblwipdispatchstateDTOListNew);
        tblwipdispatchstateService.updateBatch(tblwipdispatchstateDTOListUpdate);
    }


}
