package cn.jihong.mes.app.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.api.model.dto.ProcessLossParamDTO;
import cn.jihong.mes.api.model.vo.ProcessLossMesVO;
import cn.jihong.mes.api.service.IProcessLossService;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.oa.erp.api.model.dto.ProcessLossDTO;
import cn.jihong.oa.erp.api.model.vo.ProcessLossVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ProcessLossServiceImpl implements IProcessLossService {
    @DubboReference
    private ISffbTService sffbTService;
    @Autowired
    private RedisTemplate redisTemplate;
    @DubboReference
    private ISiteViewService siteViewService;
    @Override
    public List<ProcessLossMesVO> selectProcessLoss(ProcessLossParamDTO processLossParamDTO) {
        if (processLossParamDTO == null){
            return null;
        }
        if (StringUtil.isEmpty(processLossParamDTO.getStartDate())){
            throw new CommonException("生产年月不能为空");
        }else {
            if (!TimeTypeUtil.isLegalDate(processLossParamDTO.getStartDate().length(), processLossParamDTO.getStartDate(), "yyyy-MM")){
                throw new CommonException("生产年月不符合格式：yyyy-MM");
            }
        }
        Long companyId = SecurityUtil.getCompanyId();
        // 当公司是集团的时候，设置为厦门吉宏
        if (companyId == 100L) {
            companyId = 106L;
        }
        // 根据公司id 获得 erp 公司id
        String siteCompanyId = (String)redisTemplate.opsForValue().get(RedisCacheConstant.COMPANY_ID + companyId);
        if (StringUtils.isBlank(siteCompanyId)) {
            siteCompanyId = siteViewService.getByCompanyId(companyId.toString());
            redisTemplate.opsForValue().set(RedisCacheConstant.COMPANY_ID + companyId, siteCompanyId,1, TimeUnit.HOURS);
        }
        log.info("公司信息：" + siteCompanyId);
        ProcessLossDTO processLossDTO = new ProcessLossDTO();
        if (StringUtil.isEmpty(processLossParamDTO.getFactoryCode())){
            processLossDTO.setFactoryCode(siteCompanyId);
        }else {
            processLossDTO.setFactoryCode(processLossParamDTO.getFactoryCode());
        }

        Date startDate = DateUtil.beginOfMonth(DateUtil.parse(processLossParamDTO.getStartDate(),"yyyy-MM"));
        Date endDate = DateUtil.offsetMonth(startDate,1);
        processLossDTO.setStartDate(DateUtil.format(startDate,"yyyy-MM-dd"));
        processLossDTO.setEndDate(DateUtil.format(endDate,"yyyy-MM-dd"));
        processLossDTO.setProcessNo(processLossParamDTO.getProcessNo());
        processLossDTO.setProductCategoryNo(processLossParamDTO.getProductCategoryNo());
        List<ProcessLossVO> processLossVOS = sffbTService.selectProcessLoss(processLossDTO);
        if (CollectionUtil.isNotEmpty(processLossVOS)){
            List<ProcessLossMesVO> processLossMesVOS = new ArrayList<>();
            Map<String,List<ProcessLossVO>> processLossVOMap = processLossVOS.stream().collect(Collectors.groupingBy(o -> o.getProcessNo(),Collectors.toList()));
            for (ProcessLossVO processLossVO:processLossVOS){
                ProcessLossMesVO processLossMesVO = new ProcessLossMesVO();
                BeanUtil.copyProperties(processLossVO,processLossMesVO);
                //工单损耗率
                if (processLossVO.getDefectiveProductNum()
                        .add(processLossVO.getQualifiedQuantity()).compareTo(new BigDecimal("0"))==1){
                    processLossMesVO.setWorkOrderLossRate(processLossVO.getDefectiveProductNum()
                            .divide(processLossVO.getDefectiveProductNum()
                                    .add(processLossVO.getQualifiedQuantity()), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                }

                //开单损耗率
                if (processLossVO.getInputQuantity() != null && processLossVO.getInputQuantity().compareTo(new BigDecimal("0"))==1 ){
                    processLossMesVO.setBillingLossRate(processLossVO.getDefectiveProductNum()
                            .divide(processLossVO.getInputQuantity(), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                }

                if (processLossVOMap.containsKey(processLossVO.getProcessNo())){

                    List<ProcessLossVO> processLossMesVOSubList = processLossVOMap.get(processLossVO.getProcessNo());
                    BigDecimal defectiveProductNum = new BigDecimal("0");
                    BigDecimal qualifiedQuantity = new BigDecimal("0");
                    for (ProcessLossVO processLossVOSub:processLossMesVOSubList){
                        defectiveProductNum = defectiveProductNum.add(processLossVOSub.getDefectiveProductNum());
                        qualifiedQuantity = qualifiedQuantity.add(processLossVOSub.getQualifiedQuantity());
                    }
                    //工序损耗率
                    if (defectiveProductNum
                            .add(qualifiedQuantity).compareTo(new BigDecimal("0"))==1){
                        processLossMesVO.setProcessLossRate(defectiveProductNum
                                .divide(defectiveProductNum
                                        .add(qualifiedQuantity), 4, RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }

                }
                processLossMesVOS.add(processLossMesVO);
            }
            Comparator<ProcessLossMesVO> comparator = getComparator(processLossParamDTO.getOrderByFeild(), processLossParamDTO.getOrderType());
            Collections.sort(processLossMesVOS, comparator);
            return processLossMesVOS;

        }

        return null;
    }

    private Comparator<ProcessLossMesVO> getComparator(String field, String order) {
        Comparator<ProcessLossMesVO> comparator = null;
        if ("workOrderLossRate".equals(field)) {
            comparator = Comparator.comparing(ProcessLossMesVO::getWorkOrderLossRate);
        } else if ("processLossRate".equals(field)) {
            comparator = Comparator.comparing(ProcessLossMesVO::getProcessLossRate);
        }

        if ("desc".equals(order)) {
            comparator = comparator.reversed();
        }

        return comparator;
    }
}
