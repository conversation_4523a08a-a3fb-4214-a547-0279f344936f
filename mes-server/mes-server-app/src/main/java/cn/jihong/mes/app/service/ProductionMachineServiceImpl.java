package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.po.ProductionMachineConfigPO;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.model.po.ProductionMachinePO;
import cn.jihong.mes.api.model.vo.GetProductionMachineListInVO;
import cn.jihong.mes.api.model.vo.GetProductionMachineListOutVO;
import cn.jihong.mes.api.model.vo.ProductionMachineInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineConfigService;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.ProductionMachineMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.dto.MrbaTDTO;
import cn.jihong.oa.erp.api.model.vo.MrbaTInVO;
import cn.jihong.oa.erp.api.service.IMrbaTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 机台表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-11
 */
@Slf4j
@DubboService
public class ProductionMachineServiceImpl extends JiHongServiceImpl<ProductionMachineMapper, ProductionMachinePO>
    implements IProductionMachineService {

    @DubboReference
    private ISiteViewService siteViewService;
    @DubboReference
    private IMrbaTService mrbaTService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private IProductionMachineConfigService productionMachineConfigService;
    @Resource
    private IProductionMachineLogisticsConfigService productionMachineLogisticsConfigService;

    /**
     * 查询机台明细
     *
     * @param erpMachineId
     * @return
     */
    @Override
    public ProductionMachineOutVO getProductionMachine(String erpMachineId) {
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpMachineId, erpMachineId);
        ProductionMachinePO productionMachinePO = getOne(lambdaQueryWrapper);
        ProductionMachineOutVO productionMachineOutVO =
            BeanUtil.copyProperties(productionMachinePO, ProductionMachineOutVO.class);
        if (productionMachineOutVO == null) {
            productionMachineOutVO = new ProductionMachineOutVO();
        }

        MrbaTDTO mrbaTDTO = getMachineBaseInfo(erpMachineId);
        productionMachineOutVO.setErpMachineId(mrbaTDTO.getMrbaent() + ":" + mrbaTDTO.getMrbasite() + ":"
                + mrbaTDTO.getOocql004() + ":" + mrbaTDTO.getMrba004() + ":" + mrbaTDTO.getMrba001());
        productionMachineOutVO.setErpMachineName(mrbaTDTO.getMrba004() + ":" + mrbaTDTO.getMrba001());
        productionMachineOutVO.setProcess(mrbaTDTO.getOocql004());
        productionMachineOutVO.setCompanyCode(mrbaTDTO.getMrbasite());

        return productionMachineOutVO;
    }

    /**
     * 查询机台明细
     *
     * @param machineName
     * @return
     */
    @Override
    public ProductionMachineOutVO getProductionMachineByName(String machineName,String process) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        String companyCode = SecurityUtil.getCompanySite();
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpEquipmentName, machineName);
        lambdaQueryWrapper.eq(ProductionMachinePO::getProcess,process);
        lambdaQueryWrapper.eq(ProductionMachinePO::getCompanyCode,companyCode);
        lambdaQueryWrapper.orderByDesc(ProductionMachinePO::getId);
        List<ProductionMachinePO> productionMachinePOS = list(lambdaQueryWrapper);
//        if (CollectionUtil.isNotEmpty(productionMachinePOS) && productionMachinePOS.size() > 1){
//            throw new CommonException("设备名称重复，无法导入，请先在ERP维护好设备名称，工序："+process+";机台名称："+machineName);
//        }
        if (CollectionUtil.isEmpty(productionMachinePOS) || productionMachinePOS == null){
            throw new CommonException("查无此设备，请在机台列表详情里点保存，工序："+process+";机台名称："+machineName);
        }
        return BeanUtil.copyProperties(productionMachinePOS.get(0), ProductionMachineOutVO.class);
    }

    @Override
    public List<ProductionMachineOutVO> getProductionMachineByErpName(String machineName) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        String companyCode = SecurityUtil.getCompanySite();
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpEquipmentName, machineName)
            .eq(ProductionMachinePO::getCompanyCode, companyCode);
        List<ProductionMachinePO> productionMachinePOs = list(lambdaQueryWrapper);
        return BeanUtil.copyToList(productionMachinePOs, ProductionMachineOutVO.class);
    }

    @Override
    public List<ProductionMachineOutVO> getProductionMachineByErpMachineName(String machineName) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        String companyCode = SecurityUtil.getCompanySite();
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpMachineName, machineName)
                .eq(ProductionMachinePO::getCompanyCode, companyCode);
        List<ProductionMachinePO> productionMachinePOs = list(lambdaQueryWrapper);
        return BeanUtil.copyToList(productionMachinePOs, ProductionMachineOutVO.class);
    }

    @Override
    public List<ProductionMachineOutVO> getProductionMachineByProcessCode(String processCode) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        String companyCode = SecurityUtil.getCompanySite();
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getProcessCode, processCode)
                .eq(ProductionMachinePO::getCompanyCode, companyCode);
        List<ProductionMachinePO> productionMachinePOs = list(lambdaQueryWrapper);
        return BeanUtil.copyToList(productionMachinePOs, ProductionMachineOutVO.class);
    }

    /**
     * 查询机台明细
     *
     * @param erpMachineName
     * @return
     */
    @Override
    public ProductionMachineOutVO getProductionMachineByErpName(String erpMachineName,String process) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpMachineName, erpMachineName);
        lambdaQueryWrapper.eq(ProductionMachinePO::getProcess,process);
        lambdaQueryWrapper.eq(ProductionMachinePO::getCompanyCode,SecurityUtil.getCompanySite());
        lambdaQueryWrapper.last("limit 1");
        ProductionMachinePO productionMachinePO = getOne(lambdaQueryWrapper);
        return BeanUtil.copyProperties(productionMachinePO, ProductionMachineOutVO.class);
    }

    @Override
    public ProductionMachineOutVO getByErpNameProcessCode(String erpMachineName, String processCode) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpMachineName, erpMachineName);
        lambdaQueryWrapper.eq(ProductionMachinePO::getProcessCode,processCode);
        lambdaQueryWrapper.eq(ProductionMachinePO::getCompanyCode,SecurityUtil.getCompanySite());
        lambdaQueryWrapper.last("limit 1");
        ProductionMachinePO productionMachinePO = getOne(lambdaQueryWrapper);
        return BeanUtil.copyProperties(productionMachinePO, ProductionMachineOutVO.class);
    }

    private MrbaTDTO getMachineBaseInfo(String erpMachineName) {
        MrbaTDTO mrbaTDTO = new MrbaTDTO();
        String[] split = erpMachineName.split(":");
        // 企业编号:工厂代码:工序:机台编号
        if (split.length == 4) {
            mrbaTDTO.setMrbaent(split[0]);
            mrbaTDTO.setMrbasite(split[1]);
            mrbaTDTO.setOocql004(split[2]);
            mrbaTDTO.setMrba001(split[3]);
            mrbaTDTO = mrbaTService.getMachineBaseInfo(mrbaTDTO);
        }
        // 企业编号:工厂代码:工序:机台编号
        if (split.length == 5) {
            mrbaTDTO.setMrbaent(split[0]);
            mrbaTDTO.setMrbasite(split[1]);
            mrbaTDTO.setOocql004(split[2]);
            mrbaTDTO.setMrba001(split[4]);
            mrbaTDTO = mrbaTService.getMachineBaseInfo(mrbaTDTO);
        }
        // 可能存在其他格式的 id

        return mrbaTDTO;
    }

    /**
     * 修改机台明细
     *
     * @param productionMachineInVO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editProductionMachine(ProductionMachineInVO productionMachineInVO) {
        MrbaTDTO mrbaTDTO = getMachineBaseInfo(productionMachineInVO.getErpMachineId());// 工序:企业编号:工厂代码:机台编号
        if (mrbaTDTO.getMrba004()== null) {
            return;
        }
        if (mrbaTDTO.getMrba001() == null) {
            return;
        }
        productionMachineInVO.setErpMachineId(mrbaTDTO.getMrbaent() + ":" + mrbaTDTO.getMrbasite() + ":"
            + mrbaTDTO.getOocql004() + ":" + mrbaTDTO.getMrba004() + ":" + mrbaTDTO.getMrba001());
        // 设备名称:设备编号
        productionMachineInVO.setErpMachineName(mrbaTDTO.getMrba004() + ":" + mrbaTDTO.getMrba001());
        // 设备名称
        productionMachineInVO.setErpEquipmentName(mrbaTDTO.getMrba004());


        ProductionMachinePO productionMachinePO =
            BeanUtil.copyProperties(productionMachineInVO, ProductionMachinePO.class);

        productionMachinePO.setProcessCode(mrbaTDTO.getProcessCode());
        productionMachinePO.setFactoryCode(mrbaTDTO.getMrbaent());
        productionMachinePO.setEnterpriseNumbers(mrbaTDTO.getMrbasite());
        productionMachinePO.setProcess(mrbaTDTO.getOocql004());
        productionMachinePO.setMachineNumber(mrbaTDTO.getMrba001());
        productionMachinePO.setCompanyCode(mrbaTDTO.getMrbasite());
        productionMachinePO.setWorkPlace(mrbaTDTO.getMrbaud003());


        // 根据erpid 去做保存和修改
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpMachineId, productionMachinePO.getErpMachineId());
        ProductionMachinePO productionMachine = getOne(lambdaQueryWrapper);
        if (productionMachine == null) {
            productionMachinePO.setCreateBy(SecurityUtil.getUserId());
            save(productionMachinePO);
        } else {
            productionMachinePO.setId(productionMachine.getId());
            productionMachinePO.setUpdateBy(SecurityUtil.getUserId());
            updateById(productionMachinePO);
        }

    }

    @Override
    public Pagination<GetProductionMachineListOutVO> getProductionMachineList(GetProductionMachineListInVO getProductionMachineListInVO) {
//        Long companyId = SecurityUtil.getCompanyId();
        // 当公司是集团的时候，设置为厦门吉宏
//        if (companyId == 100L) {
//            companyId = 106L;
//        }
//        Long companyId = 125L;
        // 根据公司id 获得 erp 公司id
//        String siteCompanyId = (String)redisTemplate.opsForValue().get(RedisCacheConstant.COMPANY_ID + companyId);
//        if (StringUtils.isBlank(siteCompanyId)) {
//            siteCompanyId = siteViewService.getByCompanyId(companyId.toString());
//            redisTemplate.opsForValue().set(RedisCacheConstant.COMPANY_ID + companyId, siteCompanyId,1, TimeUnit.HOURS);
//        }
//        log.info("公司信息：" + siteCompanyId);
        String companyCode = SecurityUtil.getCompanySite();
        // 查询erp 机台信息
        MrbaTInVO mrbaTInVO = new MrbaTInVO();
        mrbaTInVO.setName(getProductionMachineListInVO.getMachineName());
        mrbaTInVO.setSiteCompanyId(companyCode);
        mrbaTInVO.setProcess(getProductionMachineListInVO.getProcess());
        mrbaTInVO.setPageNum(getProductionMachineListInVO.getPageNum());
        mrbaTInVO.setPageSize(getProductionMachineListInVO.getPageSize());
        mrbaTInVO.setState(getProductionMachineListInVO.getState());
        Pagination<MrbaTDTO> mrbaTDTOPagination = mrbaTService.getProcessMachineList(mrbaTInVO);

        // 封装数据
        List<MrbaTDTO> mrbaTDTOS = (List<MrbaTDTO>)mrbaTDTOPagination.getData();

        List<GetProductionMachineListOutVO> getProductionMachineListOutVOS = mrbaTDTOS.stream().map(mrbaTDTO -> {
            GetProductionMachineListOutVO getProductionMachineListOutVO = new GetProductionMachineListOutVO();
            getProductionMachineListOutVO.setProcess(mrbaTDTO.getOocql004());
            getProductionMachineListOutVO.setProcessNo(mrbaTDTO.getOocql002());
             getProductionMachineListOutVO.setState(mrbaTDTO.getState());
            // 企业编号:工厂代码:工序:机台编号
            getProductionMachineListOutVO.setErpMachineId(mrbaTDTO.getMrbaent() + ":" + mrbaTDTO.getMrbasite() + ":"
                    + mrbaTDTO.getOocql004() + ":" + mrbaTDTO.getMrba004() + ":" + mrbaTDTO.getMrba001());
            // 设备名称:设备编号
            getProductionMachineListOutVO.setErpMachineName(mrbaTDTO.getMrba004()+ ":" + mrbaTDTO.getMrba001());
            getProductionMachineListOutVO.setErpEquipmentName(mrbaTDTO.getMrba004());
            getProductionMachineListOutVO.setCompanyCode(mrbaTDTO.getMrbasite());
            return getProductionMachineListOutVO;
        }).collect(Collectors.toList());

        return Pagination.newInstance(getProductionMachineListOutVOS, mrbaTDTOPagination.getTotal(),mrbaTDTOPagination.getPages());

    }

    @Override
    public IPage<ProductionMachinePO> getMachineListByName(IPage page, String machineName) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        QueryWrapper<ProductionMachinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .select("DISTINCT erp_machine_name").lambda()
                .like(StringUtil.isNotEmpty(machineName), ProductionMachinePO::getErpMachineName, machineName)
                .eq(ProductionMachinePO::getCompanyCode, SecurityUtil.getCompanySite());
        return page(page, queryWrapper);
    }

    @Override
    public IPage<ProductionMachinePO> getMachineListByName(IPage page, String machineName, List<String> ignoreMachineName) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        QueryWrapper<ProductionMachinePO> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .select("DISTINCT erp_machine_name").lambda()
                .like(StringUtil.isNotEmpty(machineName), ProductionMachinePO::getErpMachineName, machineName)
                .eq(ProductionMachinePO::getCompanyCode, SecurityUtil.getCompanySite())
                .notIn(CollectionUtil.isNotEmpty(ignoreMachineName),ProductionMachinePO::getErpMachineName, ignoreMachineName);
        return page(page, queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncMachine() {
        GetProductionMachineListInVO getProductionMachineListInVO = new GetProductionMachineListInVO();
        getProductionMachineListInVO.setPageNum(1);
        getProductionMachineListInVO.setPageSize(10000);
        getProductionMachineListInVO.setCompanyCode(SecurityUtil.getCompanySite());
        Pagination<GetProductionMachineListOutVO> productionMachineList = getProductionMachineList(getProductionMachineListInVO);
        productionMachineList.getData().stream().forEach(productionMachine -> {
            ProductionMachineInVO productionMachineInVO = new ProductionMachineInVO();
            productionMachineInVO.setErpMachineId(productionMachine.getErpMachineId());
            productionMachineInVO.setStatementTime(1.00);
            editProductionMachine(productionMachineInVO);
        });

    }

    @Override
    public ProductionMachinePO getMachineById(Long id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        return getById(id);
    }

    
    
    @Override
    public void editProductionMachineLogisticsConfig(ProductionMachineInVO productionMachineInVO) {
        ProductionMachinePO productionMachinePO = getMachineById(productionMachineInVO.getId());
        // 更新机台配置
        if (productionMachinePO != null) {
            productionMachineLogisticsConfigService.saveMachineLogisticsConfig(productionMachinePO.getId(),
                productionMachineInVO.getProductionMachineLogisticsConfigVO());
        }
    }

    @Override
    public void editProductionMachineConfig(ProductionMachineInVO productionMachineInVO) {
        ProductionMachinePO productionMachinePO = getMachineById(productionMachineInVO.getId());
        // 更新机台配置
        if (productionMachinePO != null) {
            productionMachineConfigService.saveMachineConfig(productionMachinePO.getId(),
                productionMachineInVO.getProductionMachineConfigVO());

        }
    }

    @Override
    public ProductionMachineInVO.ProductionMachineConfigVO getProductionMachineConfig(Long id) {
        ProductionMachinePO productionMachinePO = getMachineById(id);
        if (productionMachinePO != null) {
            // 配置信息
            ProductionMachineConfigPO productionMachineConfigPO =
                productionMachineConfigService.getByMachineId(productionMachinePO.getId());
            ProductionMachineInVO.ProductionMachineConfigVO productionMachineConfigVO = BeanUtil
                .copyProperties(productionMachineConfigPO, ProductionMachineInVO.ProductionMachineConfigVO.class);
            return productionMachineConfigVO;
        }
        return null;
    }

    @Override
    public ProductionMachineInVO.ProductionMachineLogisticsConfigVO
        getProductionMachineLogisticsConfig(Long id) {
        ProductionMachinePO productionMachinePO = getMachineById(id);
        if (productionMachinePO != null) {
            // 配置信息
            ProductionMachineLogisticsConfigPO productionMachineLogisticsConfigPO =
                productionMachineLogisticsConfigService.getByMachineId(productionMachinePO.getId());
            ProductionMachineInVO.ProductionMachineLogisticsConfigVO productionMachineLogisticsConfigVO =
                BeanUtil.copyProperties(productionMachineLogisticsConfigPO,
                    ProductionMachineInVO.ProductionMachineLogisticsConfigVO.class);
            return productionMachineLogisticsConfigVO;
        }
        return null;
    }

    @Override
    public List<ProductionMachinePO> getByMachineName(String erpMachineName) {
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductionMachinePO::getErpMachineName, erpMachineName)
                .eq(ProductionMachinePO::getCompanyCode, SecurityUtil.getCompanySite());
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<String> getAllMachine() {
        LambdaQueryWrapper<ProductionMachinePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductionMachinePO::getCompanyCode, SecurityUtil.getCompanySite());
        List<ProductionMachinePO> list = list(lambdaQueryWrapper);
        return CollectionUtil.isNotEmpty(list)? new ArrayList<>(list.stream()
                .map(ProductionMachinePO::getErpMachineName)
                .collect(Collectors.toCollection(HashSet::new))) : null;
    }
}
