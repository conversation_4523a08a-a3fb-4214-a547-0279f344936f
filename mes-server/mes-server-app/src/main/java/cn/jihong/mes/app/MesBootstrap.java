package cn.jihong.mes.app;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * 后台管理启动类
 *
 * <AUTHOR>
 * Created on 2019/9/18
 */
@SpringBootApplication
@MapperScan("cn.jihong.mes.app.mapper")
@EnableDubbo(scanBasePackages = "cn.jihong.mes.app.service")
@EnableScheduling
public class MesBootstrap extends SpringBootServletInitializer {

	public static void main(String[] args) {
		SpringApplication.run(MesBootstrap.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(MesBootstrap.class);
	}

}
