package cn.jihong.mes.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProductCategoryDTO;
import cn.jihong.mes.api.model.dto.ProductProcessDTO;
import cn.jihong.mes.api.model.vo.ProductCategoryVO;
import cn.jihong.mes.api.model.vo.ProductProcessVO;
import cn.jihong.mes.api.service.IProductionProcesseService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 工序 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@RestController
@RequestMapping("/productionProcess")
@ShenyuSpringMvcClient(path = "/productionProcess/**")
public class ProductionProcessController {
    @Resource
    private IProductionProcesseService productionProcesseService;

    /**
     * 查询工序
     * @param productProcessDTO
     * @param pageRequest
     * @return
     */
    @PostMapping("list")
    public StandardResult<Pagination<ProductProcessVO>> getList(@RequestBody ProductProcessDTO productProcessDTO,@RequestBody @Validated PageRequest pageRequest) {
        Pagination<ProductProcessVO> pagination = productionProcesseService.getList(productProcessDTO,pageRequest);
        return StandardResult.resultCode(OperateCode.SUCCESS, pagination);
    }
}
