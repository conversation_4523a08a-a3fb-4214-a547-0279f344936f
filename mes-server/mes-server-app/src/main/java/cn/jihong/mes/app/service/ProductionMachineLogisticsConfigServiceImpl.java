package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.dto.request.UserInfo;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.model.po.ProductionMachinePO;
import cn.jihong.mes.api.model.vo.ProductionMachineInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.ProductionMachineLogisticsConfigMapper;
import cn.jihong.mes.production.api.model.constant.RedisCacheLogisticsConstant;
import cn.jihong.mes.production.api.model.dto.MeshDTO;
import cn.jihong.mes.production.api.model.enums.MeshTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.logistics.MeshByTypeAndMeshIdInVO;
import cn.jihong.mes.production.api.model.vo.out.ProductShiftOutVO;
import cn.jihong.mes.production.api.service.IMeshService;
import cn.jihong.mes.production.api.service.IProductShiftService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 机台物流相关属性 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Slf4j
@DubboService
public class ProductionMachineLogisticsConfigServiceImpl
    extends JiHongServiceImpl<ProductionMachineLogisticsConfigMapper, ProductionMachineLogisticsConfigPO>
    implements IProductionMachineLogisticsConfigService {

    @Resource
    private IProductionMachineService productionMachineService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @DubboReference
    private IProductTicketService productTicketService;
    @DubboReference
    private IProductShiftService productShiftService;
    @DubboReference
    private IA01Service ia01Service;
    @DubboReference
    private IMeshService meshService;

    @Override
    public ProductionMachineLogisticsConfigPO getMachineConfig(String machineName, String processCode) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        ProductionMachineOutVO productionMachineOutVO =
            productionMachineService.getByErpNameProcessCode(machineName, processCode);
        if (productionMachineOutVO == null) {
            throw new CommonException("查询不到该机台[" + machineName + "]工序为[" + processCode + "]");
        }
        ProductionMachineLogisticsConfigPO productionMachineConfigPO = getByMachineId(productionMachineOutVO.getId());
        if (productionMachineConfigPO == null) {
            throw new CommonException("查询不到该机台[" + machineName + "]工序为[" + processCode + "]的配置信息");
        }
        return productionMachineConfigPO;
    }


    @Override
    public ProductionMachineLogisticsConfigPO getMachineConfig(String machineName) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        List<ProductionMachineOutVO> productionMachineOutVOList =
                productionMachineService.getProductionMachineByErpMachineName(machineName);
        if (CollectionUtil.isEmpty(productionMachineOutVOList)) {
            throw new CommonException("查询不到该机台[" + machineName + "]");
        }
        List<ProductionMachineLogisticsConfigPO> productionMachineConfigPOList = getByMachineIds(productionMachineOutVOList.stream().map(ProductionMachineOutVO::getId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(productionMachineConfigPOList)) {
            throw new CommonException("查询不到该机台[" + machineName + "]的网带配置信息");
        }
        return productionMachineConfigPOList.get(0);
    }

    @Override
    public ProductionMachineLogisticsConfigPO getByMachineId(Long id) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductionMachineLogisticsConfigPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductionMachineLogisticsConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
            .eq(ProductionMachineLogisticsConfigPO::getMachineId, id);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<ProductionMachineLogisticsConfigPO> getByMachineIds(List<Long> machineIds) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        LambdaQueryWrapper<ProductionMachineLogisticsConfigPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductionMachineLogisticsConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductionMachineLogisticsConfigPO::getMachineId, machineIds);
        return list(lambdaQueryWrapper);
    }

    @Override
    public ProductionMachineOutVO getByExternalMachineId(String externalMachineId, String workshopCode) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));

        // 获得网带id 对应的类型
        MeshByTypeAndMeshIdInVO meshByTypeAndMeshIdInVO = new MeshByTypeAndMeshIdInVO();
        meshByTypeAndMeshIdInVO.setMeshId(externalMachineId);
        meshByTypeAndMeshIdInVO.setWorkshopCode(workshopCode);
        List<Integer> meshTypes = Lists.newArrayList();
        meshTypes.add(MeshTypeEnum.OUTLET.getCode());
        meshTypes.add(MeshTypeEnum.PREPARE.getCode());
        meshTypes.add(MeshTypeEnum.UP.getCode());
        meshByTypeAndMeshIdInVO.setMeshTypes(meshTypes);
        MeshDTO meshDTO = meshService.getMeshByMeshId(meshByTypeAndMeshIdInVO);

        LambdaQueryWrapper<ProductionMachineLogisticsConfigPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductionMachineLogisticsConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductionMachineLogisticsConfigPO::getWorkshopCode, workshopCode)
                .and(wrapper -> {
                            if (meshDTO.getMeshType().equals(MeshTypeEnum.SUPPLY.getCode())) {
                                wrapper.like(ProductionMachineLogisticsConfigPO::getSupplyMeshCode, externalMachineId);
                            } else if(meshDTO.getMeshType().equals(MeshTypeEnum.OUTLET.getCode())) {
                                wrapper.like(ProductionMachineLogisticsConfigPO::getExitMeshCode, externalMachineId);
                            } else if(meshDTO.getMeshType().equals(MeshTypeEnum.PREPARE.getCode())) {
                                wrapper.like(ProductionMachineLogisticsConfigPO::getPrepareMeshCode, externalMachineId);
                            } else if(meshDTO.getMeshType().equals(MeshTypeEnum.UP.getCode())) {
                                wrapper.like(ProductionMachineLogisticsConfigPO::getUpMeshCode, externalMachineId);
                            }
                        }
                );
        List<ProductionMachineLogisticsConfigPO> productionMachineConfigPOs = list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(productionMachineConfigPOs)) {
            return null;
        }
        ProductionMachineLogisticsConfigPO productionMachineConfigPO = productionMachineConfigPOs.get(0);
        ProductionMachinePO productionMachinePO = productionMachineService.getMachineById(Long.valueOf(productionMachineConfigPO.getMachineId()));

        ProductionMachineOutVO productionMachineOutVO = BeanUtil.copyProperties(productionMachinePO, ProductionMachineOutVO.class);
        productionMachineOutVO.setProductionMachineConfigVO(BeanUtil.copyProperties(productionMachineConfigPO, ProductionMachineInVO.ProductionMachineConfigVO.class));
        productionMachineOutVO.setMeshType(meshDTO.getMeshType());
        return productionMachineOutVO;
    }

    @Override
    public UserInfo getByUserInfoExternalMachineId(String externalMachineId, String workshopCode) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));
        //  根据客户机台->找到内部机台->找到机台状态->找到机台的任务id->找到任务的班组信息->调用登录接口，获得用户信息
        ProductionMachineOutVO productionMachineOutVO = getByExternalMachineId(externalMachineId,workshopCode);
        if (productionMachineOutVO == null) {
            throw new CommonException("机台不存在：" + externalMachineId);
        }
        ProductTicketPO productionTicket = productTicketService.getProductionTicket(productionMachineOutVO.getErpMachineName());
        if (productionTicket == null) {
            throw new CommonException("机台["+ productionMachineOutVO.getErpMachineName()+"]未绑定生产任务，请现在Mes上设置机台的生产任务");
        }

        // 先从Redis中查询映射关系
        String redisKey = RedisCacheLogisticsConstant.USER_INFO + productionTicket.getId();
        String cachedUserDTO = stringRedisTemplate.opsForValue().get(redisKey);
        UserDTO userDTO;

        if (cachedUserDTO != null) {
            // 如果缓存中存在，直接使用缓存数据
            userDTO = JSON.parseObject(cachedUserDTO, UserDTO.class);
            userDTO.setCompanySite(productionTicket.getCompanyCode());
            log.info("从缓存中获取机台用户映射信息: {}", cachedUserDTO);
        } else {
            List<ProductShiftOutVO> productShift = productShiftService.getProductShift(Arrays.asList(productionTicket.getId()));
            if (CollectionUtil.isEmpty(productShift)) {
                throw new CommonException("机台上无班组人员信息");
            }
            List<ProductShiftOutVO> shiftOutVOS = productShift.stream()
                    .sorted(Comparator.comparing(ProductShiftOutVO::getRoleCode)).collect(Collectors.toList());
            ProductShiftOutVO productShiftOutVO = shiftOutVOS.get(0);
            userDTO = ia01Service.getUserInfoById(productShiftOutVO.getTeamUsers());
            if (userDTO == null) {
                throw new CommonException("机台用户不存在：" + productShiftOutVO.getTeamUsers());
            }
            userDTO.setCompanySite(productionTicket.getCompanyCode());
            stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(userDTO),1, TimeUnit.HOURS);
            log.info("将机台用户映射信息存入缓存: {}", JSON.toJSONString(userDTO));
        }

        return userDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveMachineLogisticsConfig(Long id, ProductionMachineInVO.ProductionMachineLogisticsConfigVO configVO) {
        if (configVO == null) {
            return;
        }

        // 设置数据源
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum("SITE-CENTER"));

        // 获取当前机器的物流配置
        ProductionMachineLogisticsConfigPO currentConfig = getByMachineId(id);

        // 获取相同机器名称的所有机器ID
        ProductionMachinePO machine = productionMachineService.getById(id);
        List<Long> machineIds = productionMachineService.getByMachineName(machine.getErpMachineName())
                .stream()
                .map(ProductionMachinePO::getId)
                .collect(Collectors.toList());

        // 校验 SupplyMeshCode 和 ExitMeshCode 和 PrepareMeshCode 和 UpMeshCode 的唯一性
        String mm = (configVO.getExitMeshCode() + "," + configVO.getPrepareMeshCode()
                + "," + configVO.getUpMeshCode() ).replace(",null","");
        Set<String> meshCodeSet = new HashSet<>();
        List<String> allMeshCodes = Arrays.stream(mm.split(",")).map(s -> {
            return s.indexOf(":") > 0 ? s.substring(0, s.indexOf(":")) : s;
        }).collect(Collectors.toList());

        // 检查字段间重复并收集重复的网带编号
        List<String> duplicateMeshCodes = new ArrayList<>();
        for (String meshCode : allMeshCodes) {
            if (!meshCodeSet.add(meshCode)) {
                duplicateMeshCodes.add(meshCode);
            }
        }

        if (!duplicateMeshCodes.isEmpty()) {
            // 确定重复网带编号所在的字段
            List<String> conflictFields = new ArrayList<>();
            for (String duplicateCode : duplicateMeshCodes.stream().distinct().collect(Collectors.toList())) {
                List<String> fieldsWithCode = new ArrayList<>();
                if (StringUtils.isNotBlank(configVO.getExitMeshCode()) &&
                    Arrays.stream(configVO.getExitMeshCode().split(","))
                        .anyMatch(code -> (code.indexOf(":") > 0 ? code.substring(0, code.indexOf(":")) : code).equals(duplicateCode))) {
                    fieldsWithCode.add("出口网带");
                }
                if (StringUtils.isNotBlank(configVO.getPrepareMeshCode()) &&
                    Arrays.stream(configVO.getPrepareMeshCode().split(","))
                        .anyMatch(code -> (code.indexOf(":") > 0 ? code.substring(0, code.indexOf(":")) : code).equals(duplicateCode))) {
                    fieldsWithCode.add("备料网带");
                }
                if (StringUtils.isNotBlank(configVO.getUpMeshCode()) &&
                    Arrays.stream(configVO.getUpMeshCode().split(","))
                        .anyMatch(code -> (code.indexOf(":") > 0 ? code.substring(0, code.indexOf(":")) : code).equals(duplicateCode))) {
                    fieldsWithCode.add("在产网带");
                }
                if (!fieldsWithCode.isEmpty()) {
                    conflictFields.add("网带编号[" + duplicateCode + "]在" + String.join("、", fieldsWithCode) + "中重复");
                }
            }
            throw new CommonException("配置中存在重复的网带编号：" + String.join("；", conflictFields));
        }

        List<Integer> meshTypes = Lists.newArrayList();
        validateMeshCodeUnique(configVO.getSupplyMeshCode(), MeshTypeEnum.SUPPLY.getCode(), configVO.getWorkshopCode(), currentConfig, machineIds, meshTypes);

        meshTypes.add(MeshTypeEnum.OUTLET.getCode());
        meshTypes.add(MeshTypeEnum.PREPARE.getCode());
        meshTypes.add(MeshTypeEnum.UP.getCode());
        validateMeshCodeUnique(configVO.getExitMeshCode(), MeshTypeEnum.OUTLET.getCode(), configVO.getWorkshopCode(), currentConfig, machineIds,meshTypes);
        validateMeshCodeUnique(configVO.getPrepareMeshCode(), MeshTypeEnum.PREPARE.getCode(), configVO.getWorkshopCode(), currentConfig, machineIds,meshTypes);
        validateMeshCodeUnique(configVO.getUpMeshCode(), MeshTypeEnum.UP.getCode(), configVO.getWorkshopCode(), currentConfig, machineIds,meshTypes);

        // 更新或新增当前机器的物流配置
        if (currentConfig == null) {
            currentConfig = new ProductionMachineLogisticsConfigPO();
            currentConfig.setCompanyCode(SecurityUtil.getCompanySite());
            currentConfig.setMachineId(id);
        }
        updateMachineLogisticsConfig(currentConfig, configVO);
        saveOrUpdate(currentConfig);

        // 同步相同机器名称的物流配置
        syncMachineLogisticsConfig(machineIds, id, configVO, currentConfig);

        // 更新mesh
        if (StringUtils.isNotBlank(configVO.getSupplyMeshCode())) {
            meshService.updateMeshByMsehId(MeshTypeEnum.SUPPLY.getCode(), getMeshs(configVO.getSupplyMeshCode()), configVO.getWorkshopCode());
        }
        if (StringUtils.isNotBlank(configVO.getExitMeshCode())) {
            meshService.updateMeshByMsehId(MeshTypeEnum.OUTLET.getCode(), getMeshs(configVO.getExitMeshCode()),configVO.getWorkshopCode());
        }
        if (StringUtils.isNotBlank(configVO.getPrepareMeshCode())) {
            meshService.updateMeshByMsehId(MeshTypeEnum.PREPARE.getCode(), getMeshs(configVO.getPrepareMeshCode()),configVO.getWorkshopCode());
        }
        if (StringUtils.isNotBlank(configVO.getUpMeshCode())) {
            meshService.updateMeshByMsehId(MeshTypeEnum.UP.getCode(),getMeshs(configVO.getUpMeshCode()),configVO.getWorkshopCode());
        }
    }

    private List<String> getMeshs(String meshCode) {
        String[] split = meshCode.split(",");
        return Arrays.stream(split).map(s ->
            s.indexOf(":") > 0 ? s.substring(0, s.indexOf(":")) : s
        ).collect(Collectors.toList());
    }

    /**
     * 校验网带编号的唯一性
     */
    private void validateMeshCodeUnique(String meshCode, Integer meshType, String workshopCode,
                                        ProductionMachineLogisticsConfigPO currentConfig, List<Long> machineIds, List<Integer> meshTypes) {
        if (StringUtil.isNotBlank(meshCode)) {
            meshCode = meshCode.replace(":","");
            // 判断meshCode是否重复
            Set<String> meshCodeSet = new HashSet<>();
            Arrays.stream(meshCode.split(",")).map(s -> {
                return s.indexOf(":") > 0 ? s.substring(0, s.indexOf(":")) : s;
            }).forEach(meshCodeSet::add);
            if (meshCodeSet.size() != meshCode.split(",").length) {
                throw new CommonException("网带编号重复");
            }

            Arrays.stream(meshCode.split(",")).map(String::trim).forEach(code -> {
                LambdaQueryWrapper<ProductionMachineLogisticsConfigPO> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(ProductionMachineLogisticsConfigPO::getCompanyCode, SecurityUtil.getCompanySite())
                    .eq(ProductionMachineLogisticsConfigPO::getWorkshopCode, workshopCode)
                        .and(wrapper -> {
                                    if (CollectionUtil.isEmpty(meshTypes)) {
                                        wrapper.like(ProductionMachineLogisticsConfigPO::getSupplyMeshCode, code);
                                    } else {
                                        wrapper.like(ProductionMachineLogisticsConfigPO::getExitMeshCode, code)
                                                .or()
                                                .like(ProductionMachineLogisticsConfigPO::getPrepareMeshCode, code)
                                                .or()
                                                .like(ProductionMachineLogisticsConfigPO::getUpMeshCode, code);
                                    }
                                }
                        );

                if (currentConfig != null) {
                    queryWrapper.notIn(ProductionMachineLogisticsConfigPO::getMachineId, machineIds);
                }

                List<ProductionMachineLogisticsConfigPO> list = list(queryWrapper);
                if (CollectionUtil.isNotEmpty(list)) {
                    if (meshType.equals(MeshTypeEnum.SUPPLY.getCode())) {
                        throw new CommonException( "供料网带编号[" + code + "]已存在");
                    } else {
                        List<String> meshCodes = Lists.newArrayList();
                        list.stream().forEach(p->{
                            if (StringUtils.isNotBlank(p.getExitMeshCode())) {
                                meshCodes.addAll(Arrays.stream(p.getExitMeshCode().split(","))
                                        .map(ms->ms.substring(0, ms.indexOf(":"))).collect(Collectors.toList()));
                            }
                            if (StringUtils.isNotBlank(p.getPrepareMeshCode())) {
                                meshCodes.addAll(Arrays.stream(p.getPrepareMeshCode().split(",")).collect(Collectors.toList()));
                            }
                            if (StringUtils.isNotBlank(p.getUpMeshCode())) {
                                meshCodes.addAll(Arrays.stream(p.getUpMeshCode().split(",")).collect(Collectors.toList()));
                            }
                        });
                        // 比较 meshCodes 和 meshCodeSet 是否有交集
                        meshCodes.retainAll(meshCodeSet);
                        if (CollectionUtil.isNotEmpty(meshCodes)) {
                            throw new CommonException( "出口/备料/在产网带编号[" + meshCodes.stream().distinct().collect(Collectors.joining(",")) + "]已存在");
                        }
                    }
                }
            });
        }
    }

    /**
     * 更新物流配置信息
     */
    private void updateMachineLogisticsConfig(ProductionMachineLogisticsConfigPO config, ProductionMachineInVO.ProductionMachineLogisticsConfigVO configVO) {
        config.setExitMeshCode(configVO.getExitMeshCode());
        config.setSupplyMeshCode(configVO.getSupplyMeshCode());
        config.setPrepareMeshCode(configVO.getPrepareMeshCode());
        config.setUpMeshCode(configVO.getUpMeshCode());
        config.setWorkshopCode(configVO.getWorkshopCode());
        config.setPrepareMeshCount(configVO.getPrepareMeshCount());
        config.setOutboundType(configVO.getOutboundType());
    }

    /**
     * 同步相同机器名称的物流配置
     */
    private void syncMachineLogisticsConfig(List<Long> machineIds, Long currentId, ProductionMachineInVO.ProductionMachineLogisticsConfigVO configVO, ProductionMachineLogisticsConfigPO currentConfig) {
        machineIds.stream()
                .filter(machineId -> !machineId.equals(currentId)) // 跳过当前机器
                .forEach(machineId -> {
                    ProductionMachineLogisticsConfigPO config = getByMachineId(machineId);
                    if (config == null) {
                        config = new ProductionMachineLogisticsConfigPO();
                        config.setCompanyCode(SecurityUtil.getCompanySite());
                        config.setMachineId(machineId);
                    }
                    updateMachineLogisticsConfig(config, configVO);
                    saveOrUpdate(config);
                });
    }
}
